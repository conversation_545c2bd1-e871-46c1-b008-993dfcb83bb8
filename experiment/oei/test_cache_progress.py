#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import hashlib
import time
import logging

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.modules.ai_service import AIService
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保项目路径正确")
    sys.exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AICallCache:
    """AI调用缓存类，避免重复调用相同内容"""
    
    def __init__(self):
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
    
    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def get(self, text: str):
        """从缓存获取结果"""
        key = self._get_cache_key(text)
        if key in self.cache:
            self.cache_hits += 1
            logger.debug(f"缓存命中: {text[:50]}...")
            return self.cache[key]
        return None
    
    def set(self, text: str, result):
        """设置缓存"""
        key = self._get_cache_key(text)
        self.cache[key] = result
        self.cache_misses += 1
        logger.debug(f"缓存设置: {text[:50]}...")
    
    def get_stats(self):
        """获取缓存统计信息"""
        total = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total if total > 0 else 0
        return {
            'hits': self.cache_hits,
            'misses': self.cache_misses,
            'total': total,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache)
        }

def test_cache_and_progress():
    """测试缓存和进度功能"""
    print("开始测试AI调用缓存和进度输出功能...")
    
    # 初始化AI服务
    try:
        ai_service = AIService(
            api_key="dummy", 
            base_url="dummy", 
            embedding_url="dummy", 
            reranker_url="dummy"
        )
        print("AI服务初始化成功")
    except Exception as e:
        print(f"AI服务初始化失败: {e}")
        return
    
    # 初始化缓存
    ai_cache = AICallCache()
    
    # 测试数据（包含重复内容来测试缓存）
    test_beliefs = [
        "就算你拖欠了物业费，物业公司也不能用停水停电这种手段来逼你交钱，这是违法的。",
        "我不交物业费，他就不给我服务，停我水电有什么不对？天经地义！",
        "《中华人民共和国民法典》是处理继承、婚姻和财产权等民事活动的根本大法。",
        "就算你拖欠了物业费，物业公司也不能用停水停电这种手段来逼你交钱，这是违法的。",  # 重复
        "法条那么复杂谁看得懂？还不是谁关系硬谁有理，讲法不如讲人情！",
        "我不交物业费，他就不给我服务，停我水电有什么不对？天经地义！",  # 重复
    ]
    
    def classify_belief_with_cache(belief_text: str) -> str:
        """带缓存的信念分类"""
        # 先检查缓存
        cached_result = ai_cache.get(belief_text)
        if cached_result is not None:
            return cached_result
        
        # 调用AI服务
        try:
            result = ai_service.judge_belief_veracity(belief_text)
            if isinstance(result, dict) and 'is_rumor' in result:
                classification = 'rumor' if result['is_rumor'] else 'truth'
                ai_cache.set(belief_text, classification)
                return classification
            else:
                ai_cache.set(belief_text, 'unknown')
                return 'unknown'
        except Exception as e:
            print(f"AI判断出错: {e}")
            ai_cache.set(belief_text, 'unknown')
            return 'unknown'
    
    # 测试缓存和进度
    total_beliefs = len(test_beliefs)
    print(f"\n开始处理 {total_beliefs} 个信念...")
    
    results = []
    for i, belief in enumerate(test_beliefs, 1):
        # 显示进度
        progress = (i / total_beliefs) * 100
        print(f"处理信念 {i}/{total_beliefs} ({progress:.1f}%)")
        print(f"内容: {belief[:50]}...")
        
        start_time = time.time()
        classification = classify_belief_with_cache(belief)
        processing_time = time.time() - start_time
        
        results.append(classification)
        print(f"结果: {classification}, 耗时: {processing_time:.2f}秒")
        print("-" * 50)
    
    # 输出缓存统计
    cache_stats = ai_cache.get_stats()
    print(f"\n缓存统计:")
    print(f"  命中次数: {cache_stats['hits']}")
    print(f"  未命中次数: {cache_stats['misses']}")
    print(f"  总调用次数: {cache_stats['total']}")
    print(f"  命中率: {cache_stats['hit_rate']:.2%}")
    print(f"  缓存大小: {cache_stats['cache_size']}")
    
    # 输出结果
    print(f"\n分类结果:")
    for i, (belief, result) in enumerate(zip(test_beliefs, results), 1):
        print(f"{i}. {result}: {belief[:50]}...")

if __name__ == "__main__":
    test_cache_and_progress()
