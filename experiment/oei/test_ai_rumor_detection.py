#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.modules.ai_service import AIService
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保项目路径正确")
    sys.exit(1)

def test_ai_rumor_detection():
    """
    测试AI谣言检测功能
    """
    print("正在初始化AI服务...")
    try:
        # AIService需要传入参数，但实际会使用硬编码的值
        ai_service = AIService(
            api_key="dummy",
            base_url="dummy",
            embedding_url="dummy",
            reranker_url="dummy"
        )
        print("AI服务初始化成功")
    except Exception as e:
        print(f"AI服务初始化失败: {e}")
        return

    # 测试用例
    test_beliefs = [
        {
            "belief_summary": "就算你拖欠了物业费，物业公司也不能用停水停电这种手段来逼你交钱，这是违法的。",
            "expected": "truth"  # 这是一个法律事实
        },
        {
            "belief_summary": "我不交物业费，他就不给我服务，停我水电有什么不对？天经地义！",
            "expected": "rumor"  # 这是基于个人情绪的错误观点
        },
        {
            "belief_summary": "《中华人民共和国民法典》是处理继承、婚姻和财产权等民事活动的根本大法。",
            "expected": "truth"  # 这是客观事实
        },
        {
            "belief_summary": "法条那么复杂谁看得懂？还不是谁关系硬谁有理，讲法不如讲人情！",
            "expected": "rumor"  # 这是基于个人偏见的观点
        }
    ]

    print("\n开始测试AI谣言检测...")
    correct_predictions = 0
    total_predictions = 0

    for i, test_case in enumerate(test_beliefs, 1):
        belief_summary = test_case["belief_summary"]
        expected = test_case["expected"]
        
        print(f"\n测试用例 {i}:")
        print(f"信念内容: {belief_summary}")
        print(f"预期分类: {expected}")
        
        try:
            result = ai_service.judge_belief_veracity(belief_summary)
            
            if isinstance(result, dict) and 'is_rumor' in result:
                predicted = 'rumor' if result['is_rumor'] else 'truth'
                confidence = result.get('confidence', 0.0)
                reasoning = result.get('reasoning', '')
                category = result.get('category', '')
                
                print(f"AI判断结果: {predicted}")
                print(f"置信度: {confidence:.2f}")
                print(f"推理过程: {reasoning}")
                print(f"类别: {category}")
                
                if predicted == expected:
                    print("✅ 判断正确")
                    correct_predictions += 1
                else:
                    print("❌ 判断错误")
                
                total_predictions += 1
            else:
                print(f"❌ AI返回结果格式异常: {result}")
                
        except Exception as e:
            print(f"❌ AI判断出错: {e}")

    if total_predictions > 0:
        accuracy = correct_predictions / total_predictions
        print(f"\n总体准确率: {accuracy:.2%} ({correct_predictions}/{total_predictions})")
    else:
        print("\n无有效预测结果")

if __name__ == "__main__":
    test_ai_rumor_detection()
