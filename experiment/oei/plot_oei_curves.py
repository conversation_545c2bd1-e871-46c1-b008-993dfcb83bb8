#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import glob
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import logging
import argparse
from matplotlib.backends.backend_pdf import PdfPages

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置字体
plt.rcParams['font.family'] = 'Times New Roman'  # 使用Times New Roman字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_log_directory(logs_base_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/") -> str:
    """
    查找最新的日志目录（必须包含以final开头的文件）

    Args:
        logs_base_dir: 日志基础目录路径

    Returns:
        最新的日志目录路径，如果没有找到则返回None
    """
    if not os.path.exists(logs_base_dir):
        logger.error(f"日志基础目录不存在 - {logs_base_dir}")
        return None

    # 查找所有以run_开头的目录
    pattern = os.path.join(logs_base_dir, "run_*")
    directories = glob.glob(pattern)

    if not directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何run_*目录")
        return None

    # 筛选包含以final开头文件的目录
    valid_directories = []
    for directory in directories:
        final_files = glob.glob(os.path.join(directory, "final*"))
        if final_files:
            valid_directories.append(directory)
            logger.debug(f"目录 {directory} 包含 {len(final_files)} 个final文件")
        else:
            logger.debug(f"目录 {directory} 不包含final文件，跳过")

    if not valid_directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何包含final文件的目录")
        return None

    # 按修改时间排序，获取最新的目录
    valid_directories.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_dir = valid_directories[0]

    logger.info(f"找到最新的包含final文件的日志目录: {latest_dir}")
    return latest_dir

def load_beliefs_data(log_dir: str):
    """
    从日志目录加载所有beliefs数据

    Args:
        log_dir: 日志目录路径

    Returns:
        包含所有beliefs数据的字典
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    beliefs_files = glob.glob(os.path.join(log_dir, "*beliefs.json"))
    beliefs_data = {}

    for file_path in beliefs_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取轮次和阶段信息
            filename = os.path.basename(file_path)
            # 解析文件名格式：round_X_after_beliefs.json 或 round_X_before_beliefs.json
            parts = filename.replace('.json', '').split('_')
            if len(parts) >= 4 and parts[0] == 'round' and parts[2] in ['after', 'before']:
                round_num = int(parts[1])
                stage = parts[2]
                key = f"round_{round_num}_{stage}"
                beliefs_data[key] = data

        except Exception as e:
            logger.warning(f"读取文件 {file_path} 时出错: {e}")
            continue

    logger.info(f"从 {log_dir} 成功加载 {len(beliefs_data)} 个beliefs数据文件")
    return beliefs_data

def calculate_oei_for_round(beliefs_data: dict) -> float:
    """
    计算单轮的OEI值
    OEI = (社区中 |veracity_score| ≥ 0.5 的智能体数量) / (社区总智能体数量)

    Args:
        beliefs_data: 单轮的beliefs数据

    Returns:
        OEI值
    """
    if not beliefs_data:
        return 0.0

    total_agents = 0
    high_veracity_agents = 0

    for user_id, user_beliefs in beliefs_data.items():
        if not isinstance(user_beliefs, list) or not user_beliefs:
            continue
        
        total_agents += 1
        
        # 计算该用户所有belief的平均veracity_score绝对值
        veracity_scores = []
        for belief in user_beliefs:
            if isinstance(belief, dict) and 'veracity_score' in belief:
                veracity_scores.append(abs(belief['veracity_score']))
        
        if veracity_scores:
            avg_veracity = sum(veracity_scores) / len(veracity_scores)
            if avg_veracity >= 0.5:
                high_veracity_agents += 1

    if total_agents == 0:
        return 0.0

    oei = high_veracity_agents / total_agents
    return oei

def extract_oei_trends(beliefs_data: dict):
    """
    从beliefs数据中提取OEI趋势数据
    
    Args:
        beliefs_data: beliefs数据字典
        
    Returns:
        包含OEI趋势数据的字典
    """
    # 获取所有轮次
    rounds = set()
    for key in beliefs_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)
    
    rounds = sorted(list(rounds))
    logger.info(f"发现 {len(rounds)} 个轮次的数据: {rounds}")

    if not rounds:
        logger.error("未找到任何轮次数据")
        return None

    # 初始化趋势数据
    trends = {
        'rounds': rounds,
        'oei_values': []
    }
    
    # 提取每轮的OEI数据（优先使用after数据，如果没有则使用before数据）
    for round_num in rounds:
        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"
        
        # 选择数据源
        if after_key in beliefs_data:
            data = beliefs_data[after_key]
        elif before_key in beliefs_data:
            data = beliefs_data[before_key]
        else:
            logger.warning(f"轮次 {round_num} 没有找到beliefs数据")
            trends['oei_values'].append(0.0)
            continue
        
        # 计算OEI值
        oei_value = calculate_oei_for_round(data)
        trends['oei_values'].append(oei_value)
        logger.debug(f"轮次 {round_num}: OEI = {oei_value:.4f}")
    
    return trends

def plot_oei_curves(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/oei", log_dir_name: str = ""):
    """
    绘制OEI曲线图

    Args:
        trends: 趋势数据
        output_dir: 输出目录
        log_dir_name: 日志目录名称，用于创建子目录
    """
    # 创建以日志目录名命名的子目录
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)

    rounds = trends['rounds']
    oei_values = trends['oei_values']

    # 设置图形样式
    plt.style.use('default')
    # 使用深蓝色表示OEI
    oei_color = '#2E4A8B'  # 深蓝色

    # 绘制OEI曲线
    plt.figure(figsize=(6, 3))  # 调整横纵比到2:1

    # 创建进度标签（使用百分数格式）
    total_rounds = len(rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    plt.plot(rounds, oei_values, 'o-', color=oei_color,
             linewidth=2, markersize=1.5, label='OEI')

    plt.xlabel('Period', fontsize=10, fontfamily='Times New Roman')
    plt.ylabel('OEI Value', fontsize=10, fontfamily='Times New Roman')

    # 设置x轴刻度和标签 - 保留5-8个刻度
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')
    plt.yticks(fontsize=10, fontfamily='Times New Roman')

    # 设置刻度线在轴内侧
    plt.tick_params(axis='both', direction='in')

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False, prop={'family': 'Times New Roman'})
    plt.tight_layout()

    # 保存PNG格式
    oei_output_path_png = os.path.join(output_subdir, 'oei_curves.png')
    plt.savefig(oei_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    oei_output_path_pdf = os.path.join(output_subdir, 'oei_curves.pdf')
    plt.savefig(oei_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"OEI曲线已保存到: {oei_output_path_png}")
    logger.info(f"OEI曲线PDF已保存到: {oei_output_path_pdf}")

    # 保存JSON数据
    save_oei_data_json(trends, output_subdir)

def save_oei_data_json(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/oei"):
    """
    保存OEI趋势数据为JSON文件

    Args:
        trends: 趋势数据
        output_dir: 输出目录
    """
    # 构建输出数据
    output_data = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "total_rounds": len(trends['rounds']),
            "rounds_range": f"{min(trends['rounds'])}-{max(trends['rounds'])}",
            "description": "Opinion Environment Index (OEI) evolution data for virtual community experiment",
            "oei_definition": "OEI = (社区中 |veracity_score| ≥ 0.5 的智能体数量) / (社区总智能体数量)"
        },
        "rounds": trends['rounds'],
        "oei_values": trends['oei_values'],
        "statistics": {
            "min_oei": min(trends['oei_values']) if trends['oei_values'] else 0,
            "max_oei": max(trends['oei_values']) if trends['oei_values'] else 0,
            "avg_oei": sum(trends['oei_values']) / len(trends['oei_values']) if trends['oei_values'] else 0,
            "final_oei": trends['oei_values'][-1] if trends['oei_values'] else 0
        }
    }

    # 保存JSON文件
    json_output_path = os.path.join(output_dir, 'oei_curves_data.json')
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"OEI数据已保存到: {json_output_path}")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='绘制OEI曲线图')
    parser.add_argument('log_dir', nargs='?', help='日志目录路径（可选，默认使用最新目录）')

    args = parser.parse_args()

    logger.info("开始绘制OEI曲线...")

    # 1. 确定日志目录
    if args.log_dir:
        log_dir = args.log_dir
        logger.info(f"使用指定的日志目录: {log_dir}")
    else:
        log_dir = find_latest_log_directory()
        if not log_dir:
            logger.error("未找到日志目录，程序退出")
            return 1

    # 提取日志目录名称
    log_dir_name = os.path.basename(log_dir)

    # 2. 加载beliefs数据
    beliefs_data = load_beliefs_data(log_dir)
    if not beliefs_data:
        logger.error("未找到beliefs数据，程序退出")
        return 1

    # 3. 提取OEI趋势数据
    trends = extract_oei_trends(beliefs_data)
    if not trends:
        logger.error("提取OEI趋势数据失败，程序退出")
        return 1

    # 4. 绘制曲线图
    plot_oei_curves(trends, "/home/<USER>/workspace/virtualcommunity/experiment/oei", log_dir_name)

    logger.info("OEI曲线绘制完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
