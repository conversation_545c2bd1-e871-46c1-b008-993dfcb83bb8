#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import glob
from datetime import datetime
import logging
import argparse
import hashlib
import time

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    import matplotlib.pyplot as plt
    from src.modules.ai_service import AIService
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装matplotlib并且项目路径正确")
    sys.exit(1)

# 设置字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AICallCache:
    """AI调用缓存类，避免重复调用相同内容"""

    def __init__(self):
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def get(self, text: str):
        """从缓存获取结果"""
        key = self._get_cache_key(text)
        if key in self.cache:
            self.cache_hits += 1
            logger.debug(f"缓存命中: {text[:50]}...")
            return self.cache[key]
        return None

    def set(self, text: str, result):
        """设置缓存"""
        key = self._get_cache_key(text)
        self.cache[key] = result
        self.cache_misses += 1
        logger.debug(f"缓存设置: {text[:50]}...")

    def get_stats(self):
        """获取缓存统计信息"""
        total = self.cache_hits + self.cache_misses
        hit_rate = self.cache_hits / total if total > 0 else 0
        return {
            'hits': self.cache_hits,
            'misses': self.cache_misses,
            'total': total,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache)
        }

# 全局缓存实例
ai_cache = AICallCache()

def find_latest_log_directory(logs_base_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/") -> str:
    """
    查找最新的日志目录（必须包含以final开头的文件）
    """
    if not os.path.exists(logs_base_dir):
        logger.error(f"日志基础目录不存在 - {logs_base_dir}")
        return None

    # 查找所有以run_开头的目录
    pattern = os.path.join(logs_base_dir, "run_*")
    directories = glob.glob(pattern)

    if not directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何run_*目录")
        return None

    # 筛选包含以final开头文件的目录
    valid_directories = []
    for directory in directories:
        final_files = glob.glob(os.path.join(directory, "final*"))
        if final_files:
            valid_directories.append(directory)
            logger.debug(f"目录 {directory} 包含 {len(final_files)} 个final文件")
        else:
            logger.debug(f"目录 {directory} 不包含final文件，跳过")

    if not valid_directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何包含final文件的目录")
        return None

    # 按修改时间排序，获取最新的目录
    valid_directories.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_dir = valid_directories[0]

    logger.info(f"找到最新的包含final文件的日志目录: {latest_dir}")
    return latest_dir

def load_beliefs_data(log_dir: str):
    """
    从日志目录加载所有beliefs数据
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    beliefs_files = glob.glob(os.path.join(log_dir, "*beliefs.json"))
    beliefs_data = {}

    for file_path in beliefs_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取轮次和阶段信息
            filename = os.path.basename(file_path)
            # 解析文件名格式：round_X_after_beliefs.json 或 round_X_before_beliefs.json
            parts = filename.replace('.json', '').split('_')
            if len(parts) >= 4 and parts[0] == 'round' and parts[2] in ['after', 'before']:
                round_num = int(parts[1])
                stage = parts[2]
                key = f"round_{round_num}_{stage}"
                beliefs_data[key] = data

        except Exception as e:
            logger.warning(f"读取文件 {file_path} 时出错: {e}")
            continue

    logger.info(f"从 {log_dir} 成功加载 {len(beliefs_data)} 个beliefs数据文件")
    return beliefs_data

def classify_belief_by_source(belief: dict) -> str:
    """
    根据belief_source字段分类信念
    
    Args:
        belief: 信念对象
        
    Returns:
        'truth' 表示真实信息，'rumor' 表示谣言，'unknown' 表示未知
    """
    if 'belief_source' not in belief:
        return 'unknown'
    
    belief_source = belief['belief_source']
    
    # 根据需求：vernacular_saying和proposition为真实，oppositional_view是谣言
    if belief_source in ['vernacular_saying', 'proposition']:
        return 'truth'
    elif belief_source == 'oppositional_view':
        return 'rumor'
    else:
        return 'unknown'

def classify_belief_by_ai(belief: dict, ai_service: AIService) -> str:
    """
    使用AI服务判断信念是否为谣言（带缓存）

    Args:
        belief: 信念对象
        ai_service: AI服务实例

    Returns:
        'truth' 表示真实信息，'rumor' 表示谣言，'unknown' 表示判断失败
    """
    try:
        belief_summary = belief.get('belief_summary', '')
        if not belief_summary:
            return 'unknown'

        # 先检查缓存
        cached_result = ai_cache.get(belief_summary)
        if cached_result is not None:
            return cached_result

        logger.debug(f"正在使用AI判断信念: {belief_summary[:50]}...")
        result = ai_service.judge_belief_veracity(belief_summary)

        if isinstance(result, dict) and 'is_rumor' in result:
            classification = 'rumor' if result['is_rumor'] else 'truth'
            confidence = result.get('confidence', 0.0)
            logger.debug(f"AI判断结果: {classification} (置信度: {confidence:.2f})")

            # 缓存结果
            ai_cache.set(belief_summary, classification)
            return classification
        else:
            logger.warning(f"AI判断结果格式异常: {result}")
            # 缓存失败结果
            ai_cache.set(belief_summary, 'unknown')
            return 'unknown'

    except Exception as e:
        logger.warning(f"AI判断信念时出错: {e}")
        # 缓存失败结果
        ai_cache.set(belief_summary, 'unknown')
        return 'unknown'

def calculate_separated_oei_for_round(beliefs_data: dict, ai_service: AIService = None) -> dict:
    """
    计算单轮的分离OEI值（谣言和真实信息分别计算）
    
    Args:
        beliefs_data: 单轮的beliefs数据
        ai_service: AI服务实例，如果为None则使用belief_source分类
        
    Returns:
        包含谣言和真实信息OEI值的字典
    """
    if not beliefs_data:
        return {'rumor_oei': 0.0, 'truth_oei': 0.0, 'total_agents': 0}

    total_agents = 0
    rumor_high_veracity_agents = 0
    truth_high_veracity_agents = 0
    rumor_total_agents = 0
    truth_total_agents = 0

    # 计算总用户数用于进度显示
    total_users = sum(1 for user_beliefs in beliefs_data.values()
                     if isinstance(user_beliefs, list) and user_beliefs)
    processed_users = 0

    for user_id, user_beliefs in beliefs_data.items():
        if not isinstance(user_beliefs, list) or not user_beliefs:
            continue

        total_agents += 1
        processed_users += 1

        # 如果使用AI服务，显示详细进度
        if ai_service and total_users > 10:  # 只在用户数较多时显示进度
            if processed_users % max(1, total_users // 10) == 0:  # 每10%显示一次
                progress = (processed_users / total_users) * 100
                logger.debug(f"  处理用户进度: {processed_users}/{total_users} ({progress:.1f}%)")
        
        # 分别计算该用户的谣言和真实信念的平均veracity_score
        rumor_veracity_scores = []
        truth_veracity_scores = []
        
        for belief in user_beliefs:
            if not isinstance(belief, dict) or 'veracity_score' not in belief:
                continue
                
            # 判断信念类型
            if 'belief_source' in belief:
                # 使用belief_source分类
                belief_type = classify_belief_by_source(belief)
            elif ai_service:
                # 使用AI服务分类
                belief_type = classify_belief_by_ai(belief, ai_service)
            else:
                # 如果没有belief_source且没有AI服务，跳过分类，将所有信念视为unknown
                belief_type = 'unknown'
                
            veracity_score = abs(belief['veracity_score'])
            
            if belief_type == 'rumor':
                rumor_veracity_scores.append(veracity_score)
            elif belief_type == 'truth':
                truth_veracity_scores.append(veracity_score)
        
        # 计算该用户的谣言信念平均veracity_score
        if rumor_veracity_scores:
            rumor_total_agents += 1
            avg_rumor_veracity = sum(rumor_veracity_scores) / len(rumor_veracity_scores)
            if avg_rumor_veracity >= 0.5:
                rumor_high_veracity_agents += 1
        
        # 计算该用户的真实信念平均veracity_score
        if truth_veracity_scores:
            truth_total_agents += 1
            avg_truth_veracity = sum(truth_veracity_scores) / len(truth_veracity_scores)
            if avg_truth_veracity >= 0.5:
                truth_high_veracity_agents += 1

    # 计算OEI值
    rumor_oei = rumor_high_veracity_agents / rumor_total_agents if rumor_total_agents > 0 else 0.0
    truth_oei = truth_high_veracity_agents / truth_total_agents if truth_total_agents > 0 else 0.0

    return {
        'rumor_oei': rumor_oei,
        'truth_oei': truth_oei,
        'total_agents': total_agents,
        'rumor_total_agents': rumor_total_agents,
        'truth_total_agents': truth_total_agents
    }

def extract_separated_oei_trends(beliefs_data: dict, use_ai_service: bool = False):
    """
    从beliefs数据中提取分离的OEI趋势数据（谣言和真实信息分别）

    Args:
        beliefs_data: beliefs数据字典
        use_ai_service: 是否使用AI服务进行判断

    Returns:
        包含分离OEI趋势数据的字典
    """
    # 获取所有轮次
    rounds = set()
    for key in beliefs_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)

    rounds = sorted(list(rounds))
    logger.info(f"发现 {len(rounds)} 个轮次的数据: {rounds}")

    if not rounds:
        logger.error("未找到任何轮次数据")
        return None

    # 检查是否有belief_source字段
    has_belief_source = False
    for key, data in beliefs_data.items():
        if 'round_' in key and data:
            for user_id, user_beliefs in data.items():
                if isinstance(user_beliefs, list) and user_beliefs:
                    for belief in user_beliefs:
                        if isinstance(belief, dict) and 'belief_source' in belief:
                            has_belief_source = True
                            break
                    if has_belief_source:
                        break
            if has_belief_source:
                break

    if not has_belief_source:
        logger.warning("数据中没有发现belief_source字段，将自动启用AI服务进行谣言判断")
        use_ai_service = True

    # 初始化AI服务
    ai_service = None
    if use_ai_service:
        try:
            ai_service = AIService(
                api_key="dummy",
                base_url="dummy",
                embedding_url="dummy",
                reranker_url="dummy"
            )
            logger.info("AI服务初始化成功")
        except Exception as e:
            logger.error(f"AI服务初始化失败: {e}")
            if not has_belief_source:
                logger.error("无法使用AI服务且没有belief_source字段，无法进行分类")
                return None
            logger.info("将使用belief_source进行分类")

    # 初始化趋势数据
    trends = {
        'rounds': rounds,
        'rumor_oei_values': [],
        'truth_oei_values': [],
        'total_agents_counts': [],
        'rumor_agents_counts': [],
        'truth_agents_counts': []
    }
    
    # 提取每轮的分离OEI数据（优先使用after数据，如果没有则使用before数据）
    total_rounds = len(rounds)
    logger.info(f"开始处理 {total_rounds} 个轮次的数据...")

    for i, round_num in enumerate(rounds, 1):
        # 显示进度
        progress = (i / total_rounds) * 100
        logger.info(f"处理轮次 {round_num} ({i}/{total_rounds}, {progress:.1f}%)")

        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"

        # 选择数据源
        if after_key in beliefs_data:
            data = beliefs_data[after_key]
        elif before_key in beliefs_data:
            data = beliefs_data[before_key]
        else:
            logger.warning(f"轮次 {round_num} 没有找到beliefs数据")
            trends['rumor_oei_values'].append(0.0)
            trends['truth_oei_values'].append(0.0)
            trends['total_agents_counts'].append(0)
            trends['rumor_agents_counts'].append(0)
            trends['truth_agents_counts'].append(0)
            continue

        # 计算分离OEI值
        start_time = time.time()
        oei_result = calculate_separated_oei_for_round(data, ai_service)
        processing_time = time.time() - start_time

        trends['rumor_oei_values'].append(oei_result['rumor_oei'])
        trends['truth_oei_values'].append(oei_result['truth_oei'])
        trends['total_agents_counts'].append(oei_result['total_agents'])
        trends['rumor_agents_counts'].append(oei_result['rumor_total_agents'])
        trends['truth_agents_counts'].append(oei_result['truth_total_agents'])

        logger.info(f"轮次 {round_num} 完成: 谣言OEI = {oei_result['rumor_oei']:.4f}, "
                   f"真实OEI = {oei_result['truth_oei']:.4f}, 耗时 {processing_time:.2f}秒")

    # 输出缓存统计信息
    if ai_service:
        cache_stats = ai_cache.get_stats()
        logger.info(f"AI调用缓存统计: 命中 {cache_stats['hits']} 次, "
                   f"未命中 {cache_stats['misses']} 次, "
                   f"命中率 {cache_stats['hit_rate']:.2%}, "
                   f"缓存大小 {cache_stats['cache_size']}")

    return trends

def plot_separated_oei_curves(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/oei", log_dir_name: str = ""):
    """
    绘制分离的OEI曲线图（谣言和真实信息分别显示）

    Args:
        trends: 趋势数据
        output_dir: 输出目录
        log_dir_name: 日志目录名称，用于创建子目录
    """
    # 创建以日志目录名命名的子目录
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)

    rounds = trends['rounds']
    rumor_oei_values = trends['rumor_oei_values']
    truth_oei_values = trends['truth_oei_values']

    # 设置图形样式
    plt.style.use('default')
    # 使用红色表示谣言，蓝色表示真实信息
    rumor_color = '#D32F2F'  # 红色
    truth_color = '#1976D2'  # 蓝色

    # 绘制分离OEI曲线
    plt.figure(figsize=(8, 4))  # 调整横纵比

    # 创建进度标签（使用百分数格式）
    total_rounds = len(rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    plt.plot(rounds, rumor_oei_values, 'o-', color=rumor_color,
             linewidth=2, markersize=1.5, label='Rumor OEI')
    plt.plot(rounds, truth_oei_values, 'o-', color=truth_color,
             linewidth=2, markersize=1.5, label='Truth OEI')

    plt.xlabel('Period', fontsize=10, fontfamily='Times New Roman')
    plt.ylabel('OEI Value', fontsize=10, fontfamily='Times New Roman')
    plt.title('Rumor vs Truth OEI Evolution', fontsize=12, fontfamily='Times New Roman')

    # 设置x轴刻度和标签 - 保留5-8个刻度
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')
    plt.yticks(fontsize=10, fontfamily='Times New Roman')

    # 设置刻度线在轴内侧
    plt.tick_params(axis='both', direction='in')

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    oei_output_path_png = os.path.join(output_subdir, 'separated_oei_curves.png')
    plt.savefig(oei_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    oei_output_path_pdf = os.path.join(output_subdir, 'separated_oei_curves.pdf')
    plt.savefig(oei_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"分离OEI曲线已保存到: {oei_output_path_png}")
    logger.info(f"分离OEI曲线PDF已保存到: {oei_output_path_pdf}")

    # 保存JSON数据
    save_separated_oei_data_json(trends, output_subdir)

def save_separated_oei_data_json(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/oei"):
    """
    保存分离OEI趋势数据为JSON文件

    Args:
        trends: 趋势数据
        output_dir: 输出目录
    """
    # 构建输出数据
    output_data = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "total_rounds": len(trends['rounds']),
            "rounds_range": f"{min(trends['rounds'])}-{max(trends['rounds'])}",
            "description": "Separated Opinion Environment Index (OEI) evolution data for rumors vs truth",
            "oei_definition": "OEI = (社区中 |veracity_score| ≥ 0.5 的智能体数量) / (该类型信念的总智能体数量)",
            "classification_method": "基于belief_source字段：vernacular_saying和proposition为真实，oppositional_view为谣言"
        },
        "rounds": trends['rounds'],
        "rumor_oei_values": trends['rumor_oei_values'],
        "truth_oei_values": trends['truth_oei_values'],
        "total_agents_counts": trends['total_agents_counts'],
        "rumor_agents_counts": trends['rumor_agents_counts'],
        "truth_agents_counts": trends['truth_agents_counts'],
        "statistics": {
            "rumor_oei": {
                "min": min(trends['rumor_oei_values']) if trends['rumor_oei_values'] else 0,
                "max": max(trends['rumor_oei_values']) if trends['rumor_oei_values'] else 0,
                "avg": sum(trends['rumor_oei_values']) / len(trends['rumor_oei_values']) if trends['rumor_oei_values'] else 0,
                "final": trends['rumor_oei_values'][-1] if trends['rumor_oei_values'] else 0
            },
            "truth_oei": {
                "min": min(trends['truth_oei_values']) if trends['truth_oei_values'] else 0,
                "max": max(trends['truth_oei_values']) if trends['truth_oei_values'] else 0,
                "avg": sum(trends['truth_oei_values']) / len(trends['truth_oei_values']) if trends['truth_oei_values'] else 0,
                "final": trends['truth_oei_values'][-1] if trends['truth_oei_values'] else 0
            }
        }
    }

    # 保存JSON文件
    json_output_path = os.path.join(output_dir, 'separated_oei_curves_data.json')
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"分离OEI数据已保存到: {json_output_path}")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='分析谣言与真实信息的OEI曲线')
    parser.add_argument('log_dir', nargs='?', help='日志目录路径（可选，默认使用最新目录）')
    parser.add_argument('--use-ai', action='store_true', help='使用AI服务判断谣言（默认使用belief_source）')
    parser.add_argument('--specific-dir', type=str,
                       default='/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250817_205417',
                       help='显式指定的目录路径（默认: /home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250817_205417）')

    args = parser.parse_args()

    logger.info("开始分析谣言与真实信息的OEI曲线...")

    # 1. 确定日志目录
    if args.log_dir:
        log_dir = args.log_dir
        logger.info(f"使用命令行指定的日志目录: {log_dir}")
    elif args.specific_dir and os.path.exists(args.specific_dir):
        # 检查指定目录是否存在beliefs文件
        beliefs_files = glob.glob(os.path.join(args.specific_dir, "*beliefs.json"))
        if beliefs_files:
            log_dir = args.specific_dir
            logger.info(f"使用显式指定的目录: {log_dir}")
        else:
            logger.warning(f"指定目录 {args.specific_dir} 不包含beliefs文件，尝试查找最新目录")
            log_dir = find_latest_log_directory()
            if not log_dir:
                logger.error("未找到日志目录，程序退出")
                return 1
    else:
        if args.specific_dir:
            logger.warning(f"指定目录 {args.specific_dir} 不存在，尝试查找最新目录")
        log_dir = find_latest_log_directory()
        if not log_dir:
            logger.error("未找到日志目录，程序退出")
            return 1

    # 提取日志目录名称
    log_dir_name = os.path.basename(log_dir)

    # 2. 加载beliefs数据
    beliefs_data = load_beliefs_data(log_dir)
    if not beliefs_data:
        logger.error("未找到beliefs数据，程序退出")
        return 1

    # 3. 提取分离OEI趋势数据
    trends = extract_separated_oei_trends(beliefs_data, use_ai_service=args.use_ai)
    if not trends:
        logger.error("提取分离OEI趋势数据失败，程序退出")
        return 1

    # 4. 绘制曲线图
    plot_separated_oei_curves(trends, "/home/<USER>/workspace/virtualcommunity/experiment/oei", log_dir_name)

    logger.info("谣言与真实信息的OEI曲线分析完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
