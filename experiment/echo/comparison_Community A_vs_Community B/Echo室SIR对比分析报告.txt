Echo室虚拟社区SIR模型对比分析报告

生成时间：2025年08月24日 15:42:56
对比社区：Community A vs Community B
分析轮次：第1轮至第40轮，共40轮

==================================================
一、总体传播情况对比
==================================================

1. 感染峰值对比：
   - Community A：25.0%（第30轮）
   - Community B：34.0%（第19轮）
   - 差异：9.0%

2. 最终恢复率对比：
   - Community A：30.0%
   - Community B：35.0%
   - 差异：5.0%

3. 易感人群减少对比：
   - Community A：45.0%
   - Community B：53.0%
   - 差异：8.0%

==================================================
二、Exponential模型参数对比
==================================================

1. 传播率函数参数：
   - Community A: λ(t) = 0.263097·exp(-0.065857·t)
   - Community B: λ(t) = 0.326494·exp(-0.085289·t)

   参数差异：
   - λ₀差异：0.063397
   - λ₁差异：0.019432

2. 恢复率函数参数：
   - Community A: μ(t) = 0.052609·exp(-0.021698·t)
   - Community B: μ(t) = 0.021755·exp(0.021043·t)

   参数差异：
   - μ₀差异：0.030855
   - μ₁差异：0.042741

==================================================
三、群体对比分析（基于Community B数据）
==================================================

1. 知识分子群体 vs 普通群众群体：

   感染峰值对比：
   - 知识分子：20.0%（第19轮）
   - 普通群众：48.0%（第19轮）
   - 差异：28.0%

   最终恢复率对比：
   - 知识分子：34.0%
   - 普通群众：36.0%
   - 差异：2.0%

   易感人群减少对比：
   - 知识分子：38.0%
   - 普通群众：68.0%
   - 差异：30.0%

2. 群体特征分析：
   - 普通群众群体更容易感染
   - 普通群众群体恢复能力更强

==================================================
四、Echo室对比总结
==================================================

基于40轮的对比分析，Echo室实验的主要发现：

1. 社区对比：社区2传播更强

2. 群体差异：知识分子群体在信息传播中表现出不同的模式

3. 恢复能力：社区2恢复能力更强

4. 模型特征：两个社区都使用exponential变系数模型，体现了传播动力学的时变特征

5. Echo室效应：本分析特别关注了不同群体在信息传播中的差异化表现，为理解虚拟社区中的回音室效应提供了数据支持。

==================================================
报告结束
==================================================
