#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
import glob
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import logging
import argparse
from matplotlib.backends.backend_pdf import PdfPages
import seaborn as sns
import pandas as pd
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

# 设置字体为Times New Roman
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman', 'Times', 'DejaVu Serif']
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
plt.rcParams['font.size'] = 10  # 设置默认字体大小
plt.rcParams['axes.labelsize'] = 10  # 轴标签字体大小
plt.rcParams['xtick.labelsize'] = 10  # x轴刻度标签字体大小
plt.rcParams['ytick.labelsize'] = 10  # y轴刻度标签字体大小
plt.rcParams['legend.fontsize'] = 10  # 图例字体大小

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_log_directory(logs_base_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/") -> str:
    """
    查找最新的日志目录（必须包含以final开头的文件）

    Args:
        logs_base_dir: 日志基础目录路径

    Returns:
        最新的日志目录路径，如果没有找到则返回None
    """
    if not os.path.exists(logs_base_dir):
        logger.error(f"日志基础目录不存在 - {logs_base_dir}")
        return None

    # 查找所有以run_开头的目录
    pattern = os.path.join(logs_base_dir, "run_*")
    directories = glob.glob(pattern)

    if not directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何run_*目录")
        return None

    # 筛选包含以final开头文件的目录
    valid_directories = []
    for directory in directories:
        final_files = glob.glob(os.path.join(directory, "final*"))
        if final_files:
            valid_directories.append(directory)
            logger.debug(f"目录 {directory} 包含 {len(final_files)} 个final文件")
        else:
            logger.debug(f"目录 {directory} 不包含final文件，跳过")

    if not valid_directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何包含final文件的目录")
        return None

    # 按修改时间排序，获取最新的目录
    valid_directories.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_dir = valid_directories[0]

    logger.info(f"Found latest log directory with final files: {latest_dir}")
    return latest_dir

def load_agent_evolution_data(log_dir: str):
    """
    从日志目录加载所有agent_evolution数据

    Args:
        log_dir: 日志目录路径

    Returns:
        包含所有agent_evolution数据的字典
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    evolution_files = glob.glob(os.path.join(log_dir, "*agent_evolution.json"))
    evolution_data = {}

    for file_path in evolution_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取轮次和阶段信息
            filename = os.path.basename(file_path)
            # 解析文件名格式：round_X_after_agent_evolution.json 或 round_X_before_agent_evolution.json
            parts = filename.replace('.json', '').split('_')
            if len(parts) >= 4 and parts[0] == 'round' and parts[2] in ['after', 'before']:
                round_num = int(parts[1])
                stage = parts[2]
                key = f"round_{round_num}_{stage}"
                evolution_data[key] = data

        except Exception as e:
            logger.warning(f"读取文件 {file_path} 时出错: {e}")
            continue

    logger.info(f"Successfully loaded {len(evolution_data)} agent_evolution data files from {log_dir}")
    return evolution_data

def calculate_cognitive_metrics_for_round(evolution_data: dict) -> dict:
    """
    计算单轮的认知特质指标

    Args:
        evolution_data: 单轮的agent_evolution数据

    Returns:
        包含各种认知指标的字典
    """
    if not evolution_data or 'tracked_users' not in evolution_data:
        return {
            'avg_skepticism_score': 0.0,
            'avg_verification_threshold': 0.0,
            'avg_emotional_volatility': 0.0,
            'skepticism_std': 0.0,
            'verification_std': 0.0,
            'emotional_volatility_std': 0.0,
            'intellectual_avg_skepticism': 0.0,
            'regular_avg_skepticism': 0.0,
            'intellectual_avg_verification': 0.0,
            'regular_avg_verification': 0.0,
            'intellectual_avg_emotional_volatility': 0.0,
            'regular_avg_emotional_volatility': 0.0,
            'total_users': 0,
            'intellectual_users': 0,
            'regular_users': 0
        }

    skepticism_scores = []
    verification_thresholds = []
    emotional_volatilities = []
    intellectual_skepticism = []
    regular_skepticism = []
    intellectual_verification = []
    regular_verification = []
    intellectual_emotional_volatility = []
    regular_emotional_volatility = []

    total_users = 0
    intellectual_count = 0
    regular_count = 0

    for user_id, user_data in evolution_data['tracked_users'].items():
        if 'cognitive_traits' not in user_data:
            continue

        cognitive_traits = user_data['cognitive_traits']
        if 'skepticism_score' not in cognitive_traits or 'verification_threshold' not in cognitive_traits:
            continue

        skepticism = cognitive_traits['skepticism_score']
        verification = cognitive_traits['verification_threshold']
        emotional_volatility = cognitive_traits.get('emotional_volatility', 0.0)
        is_intellectual = cognitive_traits.get('is_intellectual', False)

        skepticism_scores.append(skepticism)
        verification_thresholds.append(verification)
        emotional_volatilities.append(emotional_volatility)
        total_users += 1

        if is_intellectual:
            intellectual_skepticism.append(skepticism)
            intellectual_verification.append(verification)
            intellectual_emotional_volatility.append(emotional_volatility)
            intellectual_count += 1
        else:
            regular_skepticism.append(skepticism)
            regular_verification.append(verification)
            regular_emotional_volatility.append(emotional_volatility)
            regular_count += 1

    # 计算统计指标
    metrics = {
        'avg_skepticism_score': np.mean(skepticism_scores) if skepticism_scores else 0.0,
        'avg_verification_threshold': np.mean(verification_thresholds) if verification_thresholds else 0.0,
        'avg_emotional_volatility': np.mean(emotional_volatilities) if emotional_volatilities else 0.0,
        'skepticism_std': np.std(skepticism_scores) if skepticism_scores else 0.0,
        'verification_std': np.std(verification_thresholds) if verification_thresholds else 0.0,
        'emotional_volatility_std': np.std(emotional_volatilities) if emotional_volatilities else 0.0,
        'intellectual_avg_skepticism': np.mean(intellectual_skepticism) if intellectual_skepticism else 0.0,
        'regular_avg_skepticism': np.mean(regular_skepticism) if regular_skepticism else 0.0,
        'intellectual_avg_verification': np.mean(intellectual_verification) if intellectual_verification else 0.0,
        'regular_avg_verification': np.mean(regular_verification) if regular_verification else 0.0,
        'intellectual_avg_emotional_volatility': np.mean(intellectual_emotional_volatility) if intellectual_emotional_volatility else 0.0,
        'regular_avg_emotional_volatility': np.mean(regular_emotional_volatility) if regular_emotional_volatility else 0.0,
        'total_users': total_users,
        'intellectual_users': intellectual_count,
        'regular_users': regular_count
    }

    return metrics

def extract_cognitive_trends(evolution_data: dict):
    """
    从agent_evolution数据中提取认知特质趋势数据

    Args:
        evolution_data: agent_evolution数据字典

    Returns:
        包含认知特质趋势数据的字典
    """
    # 获取所有轮次
    rounds = set()
    for key in evolution_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)

    rounds = sorted(list(rounds))
    logger.info(f"Found data for {len(rounds)} rounds: {rounds}")

    if not rounds:
        logger.error("No round data found")
        return None

    # 初始化趋势数据
    trends = {
        'rounds': rounds,
        'avg_skepticism_scores': [],
        'avg_verification_thresholds': [],
        'avg_emotional_volatilities': [],
        'skepticism_stds': [],
        'verification_stds': [],
        'emotional_volatility_stds': [],
        'intellectual_avg_skepticism': [],
        'regular_avg_skepticism': [],
        'intellectual_avg_verification': [],
        'regular_avg_verification': [],
        'intellectual_avg_emotional_volatility': [],
        'regular_avg_emotional_volatility': [],
        'total_users': [],
        'intellectual_users': [],
        'regular_users': []
    }

    # 提取每轮的认知特质数据（优先使用after数据，如果没有则使用before数据）
    for round_num in rounds:
        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"

        # 选择数据源
        if after_key in evolution_data:
            data = evolution_data[after_key]
        elif before_key in evolution_data:
            data = evolution_data[before_key]
        else:
            logger.warning(f"No agent_evolution data found for round {round_num}")
            # 填充默认值
            for key in trends.keys():
                if key != 'rounds':
                    trends[key].append(0.0)
            continue

        # 计算认知指标
        metrics = calculate_cognitive_metrics_for_round(data)

        # 添加到趋势数据
        trends['avg_skepticism_scores'].append(metrics['avg_skepticism_score'])
        trends['avg_verification_thresholds'].append(metrics['avg_verification_threshold'])
        trends['avg_emotional_volatilities'].append(metrics['avg_emotional_volatility'])
        trends['skepticism_stds'].append(metrics['skepticism_std'])
        trends['verification_stds'].append(metrics['verification_std'])
        trends['emotional_volatility_stds'].append(metrics['emotional_volatility_std'])
        trends['intellectual_avg_skepticism'].append(metrics['intellectual_avg_skepticism'])
        trends['regular_avg_skepticism'].append(metrics['regular_avg_skepticism'])
        trends['intellectual_avg_verification'].append(metrics['intellectual_avg_verification'])
        trends['regular_avg_verification'].append(metrics['regular_avg_verification'])
        trends['intellectual_avg_emotional_volatility'].append(metrics['intellectual_avg_emotional_volatility'])
        trends['regular_avg_emotional_volatility'].append(metrics['regular_avg_emotional_volatility'])
        trends['total_users'].append(metrics['total_users'])
        trends['intellectual_users'].append(metrics['intellectual_users'])
        trends['regular_users'].append(metrics['regular_users'])

        logger.debug(f"轮次 {round_num}: 平均怀疑分数 = {metrics['avg_skepticism_score']:.4f}, "
                    f"平均验证阈值 = {metrics['avg_verification_threshold']:.4f}")

    return trends

def plot_cognitive_evolution_curves(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/evolution", log_dir_name: str = ""):
    """
    绘制认知特质演化曲线图

    Args:
        trends: 趋势数据
        output_dir: 输出目录
        log_dir_name: 日志目录名称，用于创建子目录
    """
    # 创建以日志目录名命名的子目录
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)

    rounds = trends['rounds']
    skepticism_scores = trends['avg_skepticism_scores']
    verification_thresholds = trends['avg_verification_thresholds']
    emotional_volatilities = trends['avg_emotional_volatilities']

    # 设置图形样式
    plt.style.use('default')

    # 使用更高级的配色方案 - 深色调配色
    skepticism_color = '#1f4e79'    # 深海蓝
    verification_color = '#8b1538'  # 深酒红
    volatility_color = '#2d5016'    # 深森林绿

    # 设置背景色为浅灰色，增加专业感
    background_color = '#fafafa'

    # 绘制认知特质演化曲线
    fig, ax = plt.subplots(figsize=(8, 5))  # 增加高度
    fig.patch.set_facecolor(background_color)
    ax.set_facecolor(background_color)

    # 创建进度标签（使用百分数格式）
    total_rounds = len(rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    # 绘制曲线，增加阴影效果和适中的标记点
    ax.plot(rounds, skepticism_scores, 'o-', color=skepticism_color,
            linewidth=3, markersize=4, label='Skepticism Score',
            markerfacecolor='white', markeredgewidth=1.5, markeredgecolor=skepticism_color,
            alpha=0.9, zorder=3)

    ax.plot(rounds, verification_thresholds, 's-', color=verification_color,
            linewidth=3, markersize=4, label='Verification Threshold',
            markerfacecolor='white', markeredgewidth=1.5, markeredgecolor=verification_color,
            alpha=0.9, zorder=3)

    ax.plot(rounds, emotional_volatilities, '^-', color=volatility_color,
            linewidth=3, markersize=4, label='Emotional Volatility',
            markerfacecolor='white', markeredgewidth=1.5, markeredgecolor=volatility_color,
            alpha=0.9, zorder=3)

    # 添加阴影区域增强视觉效果
    ax.fill_between(rounds, skepticism_scores, alpha=0.1, color=skepticism_color, zorder=1)
    ax.fill_between(rounds, verification_thresholds, alpha=0.1, color=verification_color, zorder=1)
    ax.fill_between(rounds, emotional_volatilities, alpha=0.1, color=volatility_color, zorder=1)

    # 设置轴标签，显式指定Times New Roman字体
    ax.set_xlabel('Period', fontsize=12, fontfamily='Times New Roman', fontweight='bold')
    ax.set_ylabel('Score', fontsize=12, fontfamily='Times New Roman', fontweight='bold')

    # 设置x轴刻度和标签 - 保留5-8个刻度
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    ax.set_xticks(selected_rounds)
    ax.set_xticklabels(selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')
    ax.tick_params(axis='y', labelsize=10)

    # 设置y轴刻度标签字体
    for label in ax.get_yticklabels():
        label.set_fontfamily('Times New Roman')

    # 设置刻度线样式
    ax.tick_params(axis='both', direction='in', length=6, width=1.2, colors='#333333')
    ax.tick_params(axis='both', which='minor', direction='in', length=3, width=0.8, colors='#666666')

    # 设置网格样式
    ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.8, color='#cccccc', zorder=0)
    ax.set_axisbelow(True)

    # Calculate dynamic y-axis range based on data with optimized spacing
    all_values = skepticism_scores + verification_thresholds + emotional_volatilities
    data_min = min(all_values)
    data_max = max(all_values)
    data_range = data_max - data_min

    # Use adaptive padding based on data range
    if data_range < 0.1:  # Very small range
        padding = 0.02
    elif data_range < 0.3:  # Small range
        padding = 0.03
    else:  # Normal range
        padding = 0.05

    y_min = max(0, data_min - padding)
    y_max = min(1, data_max + padding)

    # Ensure minimum visible range
    if y_max - y_min < 0.1:
        center = (y_min + y_max) / 2
        y_min = max(0, center - 0.05)
        y_max = min(1, center + 0.05)

    ax.set_ylim(y_min, y_max)

    # 设置边框样式
    for spine in ax.spines.values():
        spine.set_linewidth(1.2)
        spine.set_color('#333333')

    # 图例 - 放在外面，无框样式
    ax.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left',
              frameon=False, prop={'family': 'Times New Roman', 'size': 10})

    plt.tight_layout()

    # 保存PNG格式
    cognitive_output_path_png = os.path.join(output_subdir, 'cognitive_evolution_curves.png')
    plt.savefig(cognitive_output_path_png, dpi=300, bbox_inches='tight', facecolor=background_color)

    # 保存PDF格式
    cognitive_output_path_pdf = os.path.join(output_subdir, 'cognitive_evolution_curves.pdf')
    plt.savefig(cognitive_output_path_pdf, bbox_inches='tight', format='pdf', facecolor=background_color)

    plt.close()
    logger.info(f"Cognitive evolution curves saved to: {cognitive_output_path_png}")
    logger.info(f"Cognitive evolution curves PDF saved to: {cognitive_output_path_pdf}")

    # 保存JSON数据
    save_cognitive_data_json(trends, output_subdir)

def save_cognitive_data_json(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/evolution"):
    """
    保存认知特质演化趋势数据为JSON文件

    Args:
        trends: 趋势数据
        output_dir: 输出目录
    """
    # 构建输出数据
    output_data = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "total_rounds": len(trends['rounds']),
            "rounds_range": f"{min(trends['rounds'])}-{max(trends['rounds'])}",
            "description": "Cognitive traits evolution data for virtual community experiment"
        },
        "rounds": trends['rounds'],
        "avg_skepticism_scores": trends['avg_skepticism_scores'],
        "avg_verification_thresholds": trends['avg_verification_thresholds'],
        "statistics": {
            "final_avg_skepticism": trends['avg_skepticism_scores'][-1] if trends['avg_skepticism_scores'] else 0,
            "final_avg_verification": trends['avg_verification_thresholds'][-1] if trends['avg_verification_thresholds'] else 0,
            "skepticism_change": (trends['avg_skepticism_scores'][-1] - trends['avg_skepticism_scores'][0]) if len(trends['avg_skepticism_scores']) > 1 else 0,
            "verification_change": (trends['avg_verification_thresholds'][-1] - trends['avg_verification_thresholds'][0]) if len(trends['avg_verification_thresholds']) > 1 else 0
        }
    }

    # 保存JSON文件
    json_output_path = os.path.join(output_dir, 'cognitive_evolution_data.json')
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"Cognitive evolution data saved to: {json_output_path}")

def load_user_data_across_rounds(log_dir: str):
    """
    加载所有轮次的用户数据，包含OCEAN人格特质和认知特质

    Args:
        log_dir: 日志目录路径

    Returns:
        包含所有轮次用户数据的字典
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    user_files = glob.glob(os.path.join(log_dir, "*users.json"))
    user_data = {}

    for file_path in user_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取轮次和阶段信息
            filename = os.path.basename(file_path)
            parts = filename.replace('.json', '').split('_')
            if len(parts) >= 4 and parts[0] == 'round' and parts[2] in ['after', 'before']:
                round_num = int(parts[1])
                stage = parts[2]
                key = f"round_{round_num}_{stage}"
                user_data[key] = data

        except Exception as e:
            logger.warning(f"读取文件 {file_path} 时出错: {e}")
            continue

    logger.info(f"Successfully loaded {len(user_data)} user data files from {log_dir}")
    return user_data

def extract_personality_cognitive_data(user_data: dict):
    """
    从用户数据中提取人格特质和认知特质的关联数据

    Args:
        user_data: 用户数据字典

    Returns:
        包含人格特质和认知特质数据的DataFrame
    """
    all_data = []

    # 获取所有轮次
    rounds = set()
    for key in user_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)

    rounds = sorted(list(rounds))

    for round_num in rounds:
        # 优先使用after数据
        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"

        if after_key in user_data:
            data = user_data[after_key]
        elif before_key in user_data:
            data = user_data[before_key]
        else:
            continue

        for user_id, user_info in data.items():
            if 'ocean_personality' not in user_info or 'cognitive_traits' not in user_info:
                continue

            ocean = user_info['ocean_personality']
            cognitive = user_info['cognitive_traits']

            # 提取数据
            row = {
                'round': round_num,
                'user_id': user_id,
                'user_group': user_info.get('user_group', 'unknown'),
                'O': ocean.get('O', 0),
                'C': ocean.get('C', 0),
                'E': ocean.get('E', 0),
                'A': ocean.get('A', 0),
                'N': ocean.get('N', 0),
                'skepticism_score': cognitive.get('skepticism_score', 0),
                'verification_threshold': cognitive.get('verification_threshold', 0),
                'emotional_volatility': cognitive.get('emotional_volatility', 0),
                'is_intellectual': cognitive.get('is_intellectual', False)
            }
            all_data.append(row)

    df = pd.DataFrame(all_data)
    logger.info(f"Extracted personality-cognitive data: {len(df)} records across {len(rounds)} rounds")
    return df

def plot_personality_cognitive_heatmap(df: pd.DataFrame, output_dir: str, log_dir_name: str = ""):
    """
    Plot personality traits and cognitive traits correlation heatmap

    Args:
        df: DataFrame containing personality and cognitive traits data
        output_dir: Output directory
        log_dir_name: Log directory name
    """
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)

    # Use last round data for correlation analysis
    last_round = df['round'].max()
    last_round_data = df[df['round'] == last_round]

    # Select relevant features
    features = ['O', 'C', 'E', 'A', 'N', 'skepticism_score', 'verification_threshold', 'emotional_volatility']
    correlation_data = last_round_data[features].corr()

    # Create heatmap
    plt.figure(figsize=(10, 8))

    # Use enhanced Morandi blue-orange color scheme with better contrast
    morandi_blue = '#5A7A8F'    # Deeper Morandi blue
    morandi_orange = '#C4956B'  # Deeper Morandi orange
    morandi_light_blue = '#8FA5B5'   # Light Morandi blue for near-zero values
    morandi_light_orange = '#D4B08A' # Light Morandi orange for near-zero values
    morandi_neutral = '#E8E2D8'      # Neutral Morandi beige

    # Create a more nuanced color map with better distribution
    colors = [morandi_blue, morandi_light_blue, morandi_neutral, morandi_light_orange, morandi_orange]
    n_bins = 100
    cmap = LinearSegmentedColormap.from_list('enhanced_morandi', colors, N=n_bins)

    # Use standard correlation range from -1 to 1
    # Remove mask to show full correlation matrix
    heatmap = sns.heatmap(correlation_data,
                         annot=True,
                         cmap=cmap,
                         center=0,
                         vmin=-1,
                         vmax=1,
                         square=True,
                         fmt='.2f',
                         cbar_kws={"shrink": .8},
                         annot_kws={'size': 10, 'family': 'Times New Roman'})

    # Set English labels - position them at the center of each cell
    labels = ['Openness', 'Conscientiousness', 'Extraversion', 'Agreeableness', 'Neuroticism',
              'Skepticism Score', 'Verification Threshold', 'Emotional Volatility']

    # Set x-axis labels at center of cells with Times New Roman font
    plt.xticks(np.arange(len(features)) + 0.5, labels, rotation=45, ha='right',
               fontfamily='Times New Roman', fontsize=10)
    # Set y-axis labels at center of cells with Times New Roman font
    plt.yticks(np.arange(len(features)) + 0.5, labels, rotation=0,
               fontfamily='Times New Roman', fontsize=10)

    # Set colorbar labels font
    cbar = heatmap.collections[0].colorbar
    cbar.ax.tick_params(labelsize=10)
    for label in cbar.ax.get_yticklabels():
        label.set_fontfamily('Times New Roman')

    plt.tight_layout()

    # Save PNG figure
    heatmap_path_png = os.path.join(output_subdir, 'personality_cognitive_heatmap.png')
    plt.savefig(heatmap_path_png, dpi=300, bbox_inches='tight')

    # Save PDF figure
    heatmap_path_pdf = os.path.join(output_subdir, 'personality_cognitive_heatmap.pdf')
    plt.savefig(heatmap_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()

    logger.info(f"Personality-cognitive heatmap PNG saved to: {heatmap_path_png}")
    logger.info(f"Personality-cognitive heatmap PDF saved to: {heatmap_path_pdf}")

def generate_txt_report(df: pd.DataFrame, trends: dict, output_dir: str, log_dir_name: str = ""):
    """
    Generate text report for heatmap and cognitive evolution curves

    Args:
        df: DataFrame containing personality and cognitive traits data
        trends: Cognitive evolution trends data
        output_dir: Output directory
        log_dir_name: Log directory name
    """
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)

    # Use last round data for correlation analysis
    last_round = df['round'].max()
    first_round = df['round'].min()
    last_round_data = df[df['round'] == last_round]

    # Calculate correlations
    features = ['O', 'C', 'E', 'A', 'N', 'skepticism_score', 'verification_threshold', 'emotional_volatility']
    correlation_data = last_round_data[features].corr()

    # Calculate changes over time
    skepticism_change = trends['avg_skepticism_scores'][-1] - trends['avg_skepticism_scores'][0]
    verification_change = trends['avg_verification_thresholds'][-1] - trends['avg_verification_thresholds'][0]
    volatility_change = trends['avg_emotional_volatilities'][-1] - trends['avg_emotional_volatilities'][0]

    # Generate report content
    report_content = f"""COGNITIVE TRAITS ANALYSIS REPORT
=====================================

Analysis Period: Round {first_round} to Round {last_round}
Total Rounds: {len(trends['rounds'])}
Total Users: {df['user_id'].nunique()}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

1. COGNITIVE EVOLUTION CURVES ANALYSIS
=====================================

The cognitive evolution curves show the temporal changes in three key cognitive traits:

1.1 Skepticism Score Evolution:
- Initial Value: {trends['avg_skepticism_scores'][0]:.4f}
- Final Value: {trends['avg_skepticism_scores'][-1]:.4f}
- Total Change: {skepticism_change:+.4f} ({skepticism_change/trends['avg_skepticism_scores'][0]*100:+.2f}%)
- Trend: {'Increasing' if skepticism_change > 0 else 'Decreasing' if skepticism_change < 0 else 'Stable'}

1.2 Verification Threshold Evolution:
- Initial Value: {trends['avg_verification_thresholds'][0]:.4f}
- Final Value: {trends['avg_verification_thresholds'][-1]:.4f}
- Total Change: {verification_change:+.4f} ({verification_change/trends['avg_verification_thresholds'][0]*100:+.2f}%)
- Trend: {'Increasing' if verification_change > 0 else 'Decreasing' if verification_change < 0 else 'Stable'}

1.3 Emotional Volatility Evolution:
- Initial Value: {trends['avg_emotional_volatilities'][0]:.4f}
- Final Value: {trends['avg_emotional_volatilities'][-1]:.4f}
- Total Change: {volatility_change:+.4f} ({volatility_change/trends['avg_emotional_volatilities'][0]*100:+.2f}%)
- Trend: {'Increasing' if volatility_change > 0 else 'Decreasing' if volatility_change < 0 else 'Stable'}

2. PERSONALITY-COGNITIVE CORRELATION HEATMAP ANALYSIS
===================================================

The correlation heatmap reveals relationships between OCEAN personality traits and cognitive traits:

2.1 Key Personality-Cognitive Correlations:
- Openness ↔ Skepticism Score: {correlation_data.loc['O', 'skepticism_score']:.3f}
- Openness ↔ Verification Threshold: {correlation_data.loc['O', 'verification_threshold']:.3f}
- Conscientiousness ↔ Skepticism Score: {correlation_data.loc['C', 'skepticism_score']:.3f}
- Conscientiousness ↔ Verification Threshold: {correlation_data.loc['C', 'verification_threshold']:.3f}
- Extraversion ↔ Skepticism Score: {correlation_data.loc['E', 'skepticism_score']:.3f}
- Extraversion ↔ Verification Threshold: {correlation_data.loc['E', 'verification_threshold']:.3f}
- Agreeableness ↔ Skepticism Score: {correlation_data.loc['A', 'skepticism_score']:.3f}
- Agreeableness ↔ Verification Threshold: {correlation_data.loc['A', 'verification_threshold']:.3f}
- Neuroticism ↔ Skepticism Score: {correlation_data.loc['N', 'skepticism_score']:.3f}
- Neuroticism ↔ Verification Threshold: {correlation_data.loc['N', 'verification_threshold']:.3f}

2.2 Cognitive Traits Inter-correlations:
- Skepticism Score ↔ Verification Threshold: {correlation_data.loc['skepticism_score', 'verification_threshold']:.3f}
- Skepticism Score ↔ Emotional Volatility: {correlation_data.loc['skepticism_score', 'emotional_volatility']:.3f}
- Verification Threshold ↔ Emotional Volatility: {correlation_data.loc['verification_threshold', 'emotional_volatility']:.3f}

2.3 Strongest Correlations (|r| > 0.3):"""

    # Find strongest correlations
    strong_correlations = []
    for i in range(len(features)):
        for j in range(i+1, len(features)):
            corr_val = correlation_data.iloc[i, j]
            if abs(corr_val) > 0.3:
                strong_correlations.append((features[i], features[j], corr_val))

    strong_correlations.sort(key=lambda x: abs(x[2]), reverse=True)

    if strong_correlations:
        for trait1, trait2, corr in strong_correlations:
            trait1_name = {'O': 'Openness', 'C': 'Conscientiousness', 'E': 'Extraversion',
                          'A': 'Agreeableness', 'N': 'Neuroticism', 'skepticism_score': 'Skepticism Score',
                          'verification_threshold': 'Verification Threshold', 'emotional_volatility': 'Emotional Volatility'}
            report_content += f"\n- {trait1_name[trait1]} ↔ {trait1_name[trait2]}: {corr:.3f}"
    else:
        report_content += "\n- No correlations with |r| > 0.3 found"

    report_content += f"""

3. INTERPRETATION AND INSIGHTS
=============================

3.1 Cognitive Evolution Patterns:
The analysis reveals {'consistent upward trends' if all([skepticism_change > 0, verification_change > 0]) else 'mixed patterns'} in cognitive traits over time.
{'Users are becoming more skeptical and cautious in their information processing.' if skepticism_change > 0 and verification_change > 0 else 'Cognitive traits show varied evolution patterns.'}

3.2 Personality-Cognitive Relationships:
The correlation analysis shows {'strong' if any([abs(correlation_data.loc['N', 'skepticism_score']) > 0.3, abs(correlation_data.loc['skepticism_score', 'verification_threshold']) > 0.3]) else 'moderate'} relationships between personality and cognitive traits.
Neuroticism shows {'positive' if correlation_data.loc['N', 'skepticism_score'] > 0 else 'negative'} correlation with skepticism, suggesting that more neurotic individuals tend to be {'more' if correlation_data.loc['N', 'skepticism_score'] > 0 else 'less'} skeptical.

3.3 Methodological Notes:
- Color scheme: Morandi blue-orange palette for optimal contrast
- Correlation values range from -1 (perfect negative) to +1 (perfect positive)
- Analysis based on final round data (Round {last_round}) for correlations
- Evolution curves show population averages across all rounds

4. TECHNICAL SPECIFICATIONS
==========================

4.1 Visualization Details:
- Heatmap: 8x8 correlation matrix with full display (no masking)
- Evolution Curves: Three-line plot with Times New Roman font
- Color Scheme: Morandi blue (#7B9AAF) to orange (#D4A574) gradient
- Export Formats: PNG (300 DPI) and PDF for both visualizations

4.2 Data Processing:
- Missing values handled through mean imputation
- Standardization applied for correlation calculations
- Temporal aggregation: round-wise population means

=====================================
End of Report
"""

    # Save report
    report_path = os.path.join(output_subdir, 'cognitive_analysis_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    logger.info(f"Text analysis report saved to: {report_path}")

# Removed plot_personality_clusters_evolution function as requested

# Removed plot_cognitive_trajectory_3d and plot_ocean_radar_by_cognitive_level functions as requested

def plot_combined_cognitive_analysis(trends: dict, df: pd.DataFrame, output_dir: str, log_dir_name: str = ""):
    """
    绘制合并的认知演化曲线和人格-认知特质热力图

    Args:
        trends: 认知演化趋势数据
        df: 包含人格和认知特质数据的DataFrame
        output_dir: 输出目录
        log_dir_name: 日志目录名称
    """
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)

    # 创建合并图表 - 调整左右子图宽度比例，左图减小约4cm
    # 使用width_ratios控制子图宽度比例：左图较窄，右图较宽
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6), gridspec_kw={'width_ratios': [7,8]})

    # 左侧：认知演化曲线
    rounds = trends['rounds']
    skepticism_scores = trends['avg_skepticism_scores']
    verification_thresholds = trends['avg_verification_thresholds']
    emotional_volatilities = trends['avg_emotional_volatilities']

    # 使用更高级的配色方案 - 深色调配色
    skepticism_color = '#1f4e79'    # 深海蓝
    verification_color = '#8b1538'  # 深酒红
    volatility_color = '#2d5016'    # 深森林绿

    # 设置背景色为浅灰色，增加专业感
    background_color = '#fafafa'
    ax1.set_facecolor(background_color)

    # 创建进度标签（使用百分数格式）
    total_rounds = len(rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    # 绘制曲线，增加阴影效果和适中的标记点
    ax1.plot(rounds, skepticism_scores, 'o-', color=skepticism_color,
             linewidth=3, markersize=4, label='Skepticism Score',
             markerfacecolor='white', markeredgewidth=1.5, markeredgecolor=skepticism_color,
             alpha=0.9, zorder=3)
    ax1.plot(rounds, verification_thresholds, 's-', color=verification_color,
             linewidth=3, markersize=4, label='Verification Threshold',
             markerfacecolor='white', markeredgewidth=1.5, markeredgecolor=verification_color,
             alpha=0.9, zorder=3)
    ax1.plot(rounds, emotional_volatilities, '^-', color=volatility_color,
             linewidth=3, markersize=4, label='Emotional Volatility',
             markerfacecolor='white', markeredgewidth=1.5, markeredgecolor=volatility_color,
             alpha=0.9, zorder=3)

    # 添加阴影区域增强视觉效果
    ax1.fill_between(rounds, skepticism_scores, alpha=0.1, color=skepticism_color, zorder=1)
    ax1.fill_between(rounds, verification_thresholds, alpha=0.1, color=verification_color, zorder=1)
    ax1.fill_between(rounds, emotional_volatilities, alpha=0.1, color=volatility_color, zorder=1)

    # 设置轴标签，显式指定Times New Roman字体
    ax1.set_xlabel('Period', fontsize=12, fontfamily='Times New Roman', fontweight='bold')
    ax1.set_ylabel('Score', fontsize=12, fontfamily='Times New Roman', fontweight='bold')

    # 设置x轴刻度和标签 - 保留5-8个刻度
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    ax1.set_xticks(selected_rounds)
    ax1.set_xticklabels(selected_labels, fontsize=10, rotation=45, fontfamily='Times New Roman')
    ax1.tick_params(axis='y', labelsize=10)

    # 设置y轴刻度标签字体
    for label in ax1.get_yticklabels():
        label.set_fontfamily('Times New Roman')

    # 设置刻度线样式
    ax1.tick_params(axis='both', direction='in', length=6, width=1.2, colors='#333333')
    ax1.tick_params(axis='both', which='minor', direction='in', length=3, width=0.8, colors='#666666')

    # 设置网格样式
    ax1.grid(True, alpha=0.3, linestyle='--', linewidth=0.8, color='#cccccc', zorder=0)
    ax1.set_axisbelow(True)

    # Calculate dynamic y-axis range based on data with optimized spacing
    all_values = skepticism_scores + verification_thresholds + emotional_volatilities
    data_min = min(all_values)
    data_max = max(all_values)
    data_range = data_max - data_min

    # Use adaptive padding based on data range
    if data_range < 0.1:  # Very small range
        padding = 0.02
    elif data_range < 0.3:  # Small range
        padding = 0.03
    else:  # Normal range
        padding = 0.05

    y_min = max(0, data_min - padding)
    y_max = min(1, data_max + padding)

    # Ensure minimum visible range
    if y_max - y_min < 0.1:
        center = (y_min + y_max) / 2
        y_min = max(0, center - 0.05)
        y_max = min(1, center + 0.05)

    ax1.set_ylim(y_min, y_max)

    # 设置边框样式
    for spine in ax1.spines.values():
        spine.set_linewidth(1.2)
        spine.set_color('#333333')

    # 图例放在下方，水平排列，无框样式
    ax1.legend(fontsize=10, loc='upper center', bbox_to_anchor=(0.5, -0.15),
               ncol=3, frameon=False, prop={'family': 'Times New Roman', 'size': 10})

    # 右侧：人格-认知特质热力图
    # 使用最后一轮数据进行相关性分析
    last_round = df['round'].max()
    last_round_data = df[df['round'] == last_round]

    # 选择相关特征
    features = ['O', 'C', 'E', 'A', 'N', 'skepticism_score', 'verification_threshold', 'emotional_volatility']
    correlation_data = last_round_data[features].corr()

    # 使用增强的Morandi蓝橙配色方案
    morandi_blue = '#5A7A8F'    # 深Morandi蓝
    morandi_orange = '#C4956B'  # 深Morandi橙
    morandi_light_blue = '#8FA5B5'   # 浅Morandi蓝
    morandi_light_orange = '#D4B08A' # 浅Morandi橙
    morandi_neutral = '#E8E2D8'      # 中性Morandi米色

    # 创建更细致的颜色映射
    colors = [morandi_blue, morandi_light_blue, morandi_neutral, morandi_light_orange, morandi_orange]
    n_bins = 100
    cmap = LinearSegmentedColormap.from_list('enhanced_morandi', colors, N=n_bins)

    # 使用标准相关系数范围从-1到1
    sns.heatmap(correlation_data,
                annot=True,
                cmap=cmap,
                center=0,
                vmin=-1,
                vmax=1,
                square=True,
                fmt='.2f',
                cbar_kws={"shrink": .8},
                annot_kws={'size': 10, 'family': 'Times New Roman'},
                ax=ax2)

    # 设置英文标签
    labels = ['Openness', 'Conscientiousness', 'Extraversion', 'Agreeableness', 'Neuroticism',
              'Skepticism Score', 'Verification Threshold', 'Emotional Volatility']

    # 设置x轴和y轴标签在单元格中心，显式指定Times New Roman字体
    ax2.set_xticks(np.arange(len(features)) + 0.5)
    ax2.set_xticklabels(labels, rotation=45, ha='right', fontfamily='Times New Roman', fontsize=10)
    ax2.set_yticks(np.arange(len(features)) + 0.5)
    ax2.set_yticklabels(labels, rotation=0, fontfamily='Times New Roman', fontsize=10)

    # 设置colorbar标签字体
    cbar = ax2.collections[0].colorbar
    cbar.ax.tick_params(labelsize=10)
    for label in cbar.ax.get_yticklabels():
        label.set_fontfamily('Times New Roman')

    # 设置整体图形背景色
    fig.patch.set_facecolor(background_color)

    # 调整子图间距，减小两图之间的距离
    plt.tight_layout()
    plt.subplots_adjust(wspace=0.1)  # 默认wspace约为0.4，减半到0.1

    # 保存PNG格式
    combined_output_path_png = os.path.join(output_subdir, 'combined_cognitive_analysis.png')
    plt.savefig(combined_output_path_png, dpi=300, bbox_inches='tight', facecolor=background_color)

    # 保存PDF格式
    combined_output_path_pdf = os.path.join(output_subdir, 'combined_cognitive_analysis.pdf')
    plt.savefig(combined_output_path_pdf, bbox_inches='tight', format='pdf', facecolor=background_color)

    plt.close()
    logger.info(f"Combined cognitive analysis PNG saved to: {combined_output_path_png}")
    logger.info(f"Combined cognitive analysis PDF saved to: {combined_output_path_pdf}")

def plot_advanced_personality_cognitive_analysis(log_dir: str, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/evolution", trends: dict = None):
    """
    执行高级的人格特质与认知特质关联分析和可视化

    Args:
        log_dir: 日志目录路径
        output_dir: 输出目录
        trends: 认知演化趋势数据（用于生成报告）
    """
    logger.info("开始高级人格-认知特质分析...")

    # 提取日志目录名称
    log_dir_name = os.path.basename(log_dir)

    # 1. 加载用户数据
    user_data = load_user_data_across_rounds(log_dir)
    if not user_data:
        logger.error("无法加载用户数据")
        return False

    # 2. 提取人格和认知特质数据
    df = extract_personality_cognitive_data(user_data)
    if df.empty:
        logger.error("无法提取人格-认知特质数据")
        return False

    logger.info(f"成功提取数据: {len(df)} 条记录，涵盖 {df['round'].nunique()} 轮次")

    # 3. 生成可视化
    try:
        # 人格特质与认知特质关联热力图
        plot_personality_cognitive_heatmap(df, output_dir, log_dir_name)

        # 如果有trends数据，生成合并图表
        if trends:
            plot_combined_cognitive_analysis(trends, df, output_dir, log_dir_name)

        logger.info("热力图生成完成！")

        # 4. 生成txt分析报告
        if trends:
            generate_txt_report(df, trends, output_dir, log_dir_name)

        return True

    except Exception as e:
        logger.error(f"生成可视化图表时出错: {e}")
        return False

# Removed generate_analysis_report function as requested

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='Plot cognitive traits evolution curves and advanced personality analysis')
    parser.add_argument('log_dir', nargs='?', help='Log directory path (optional, uses latest directory by default)')
    parser.add_argument('--advanced', action='store_true', help='Generate advanced personality-cognitive analysis')
    parser.add_argument('--basic', action='store_true', help='Generate basic cognitive evolution curves only')

    args = parser.parse_args()

    logger.info("Starting cognitive traits analysis...")

    # 1. 确定日志目录
    if args.log_dir:
        log_dir = args.log_dir
        logger.info(f"Using specified log directory: {log_dir}")
    else:
        log_dir = find_latest_log_directory()
        if not log_dir:
            logger.error("No log directory found, exiting")
            return 1

    # 提取日志目录名称
    log_dir_name = os.path.basename(log_dir)

    success = True

    # 2. 基础认知特质演化曲线
    if not args.advanced or args.basic:
        logger.info("Generating basic cognitive evolution curves...")

        # 加载agent_evolution数据
        evolution_data = load_agent_evolution_data(log_dir)
        if not evolution_data:
            logger.error("No agent_evolution data found for basic analysis")
            success = False
        else:
            # 提取认知特质趋势数据
            trends = extract_cognitive_trends(evolution_data)
            if not trends:
                logger.error("Failed to extract cognitive traits trends data")
                success = False
            else:
                # 绘制曲线图
                plot_cognitive_evolution_curves(trends, "/home/<USER>/workspace/virtualcommunity/experiment/evolution", log_dir_name)
                logger.info("Basic cognitive evolution curves completed!")

    # 3. 高级人格-认知特质分析
    if args.advanced or not args.basic:
        logger.info("Generating advanced personality-cognitive analysis...")

        # 如果有trends数据，传递给高级分析函数
        trends_data = None
        if 'trends' in locals():
            trends_data = trends

        advanced_success = plot_advanced_personality_cognitive_analysis(
            log_dir, "/home/<USER>/workspace/virtualcommunity/experiment/evolution", trends_data
        )

        if not advanced_success:
            logger.error("Advanced analysis failed")
            success = False
        else:
            logger.info("Advanced personality-cognitive analysis completed!")

    if success:
        logger.info("All cognitive traits analysis completed successfully!")
        return 0
    else:
        logger.error("Some analysis tasks failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
