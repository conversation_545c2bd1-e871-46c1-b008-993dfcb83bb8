================================================================================
SIR模型比较分析报告
================================================================================
生成时间: 2025-08-24 15:40:56

模型说明:
1. 常系数模型: dS/dt = -β·S·I/N, dI/dt = β·S·I/N - γ·I, dR/dt = γ·I
2. 线性变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t, μ(t) = μ₀ + μ₁·t
3. 指数变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀·exp(λ₁·t), μ(t) = μ₀·exp(μ₁·t)
4. 多项式变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t + λ₂·t², μ(t) = μ₀ + μ₁·t + μ₂·t²

最佳模型总结:
----------------------------------------
总群体: exponential模型 (MSE: 0.00138844)

------------------------------------------------------------
总群体 详细比较结果
------------------------------------------------------------

1. exponential模型:
   MSE: 0.00138844
   模型类型: 指数变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.326494·exp(-0.085289·t)
     恢复率: μ(t) = 0.021755·exp(0.021043·t)
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -0.326494·exp(-0.085289·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = 0.326494·exp(-0.085289·t)·I·S - 0.021755·exp(0.021043·t)·I
     dR/dt = μ(t)·I = 0.021755·exp(0.021043·t)·I
   平均R₀: 2.570759
   原始参数: [0.32649407183206114, -0.08528882408098878, 0.02175470029995991, 0.02104334696957037]
   R² (S,I,R): (0.9645, 0.8804, 0.9835)
   ★ 最佳模型

2. linear模型:
   MSE: 0.00255845
   模型类型: 线性变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.243665 + -0.008537·t
     恢复率: μ(t) = 0.023285 + 0.000534·t
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.243665 + -0.008537·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.243665 + -0.008537·t)·I·S - (0.023285 + 0.000534·t)·I
     dR/dt = μ(t)·I = (0.023285 + 0.000534·t)·I
   平均R₀: 2.459932
   原始参数: [0.24366537970733523, -0.00853675606891098, 0.023285089922246576, 0.000534180138900433]
   R² (S,I,R): (0.9288, 0.7626, 0.9831)

3. polynomial模型:
   MSE: 0.00462234
   模型类型: 多项式变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.155119 + 0.015075·t + -0.001027·t²
     恢复率: μ(t) = 0.047901 + -0.001646·t + 0.000039·t²
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.155119 + 0.015075·t + -0.001027·t²)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.155119 + 0.015075·t + -0.001027·t²)·I·S - (0.047901 + -0.001646·t + 0.000039·t²)·I
     dR/dt = μ(t)·I = (0.047901 + -0.001646·t + 0.000039·t²)·I
   平均R₀: 2.334100
   原始参数: [0.1551190608370021, 0.015074805929108929, -0.0010273945851198873, 0.047900798697430046, -0.0016456964549294445, 3.9372810414872385e-05]
   R² (S,I,R): (0.8687, 0.6088, 0.9624)

4. constant模型:
   MSE: 0.01685771
   模型类型: 常系数模型
   拟合后的率函数:
     传播率: β = 0.144410
     恢复率: γ = 0.052681
   拟合后的微分方程:
     dS/dt = -0.144410·S·I/N
     dI/dt = 0.144410·S·I/N - 0.052681·I
     dR/dt = 0.052681·I
   R₀: 2.741218
   R² (S,I,R): (0.5986, -1.1084, 0.9471)

============================================================
模型选择建议
============================================================
最佳模型: exponential模型

建议:
1. exponential变系数模型表现最佳，说明传播动力学存在时变特征
2. 变系数模型能更好地捕捉传播过程中的动态变化
3. 建议在实际应用中使用变系数模型进行预测和分析
