================================================================================
SIR模型比较分析报告
================================================================================
生成时间: 2025-08-24 15:39:03

模型说明:
1. 常系数模型: dS/dt = -β·S·I/N, dI/dt = β·S·I/N - γ·I, dR/dt = γ·I
2. 线性变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t, μ(t) = μ₀ + μ₁·t
3. 指数变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀·exp(λ₁·t), μ(t) = μ₀·exp(μ₁·t)
4. 多项式变系数模型: dS/dt = -λ(t)·I·S, dI/dt = λ(t)·I·S - μ(t)·I, dR/dt = μ(t)·I
   其中 λ(t) = λ₀ + λ₁·t + λ₂·t², μ(t) = μ₀ + μ₁·t + μ₂·t²

最佳模型总结:
----------------------------------------
总群体: exponential模型 (MSE: 0.00116518)

------------------------------------------------------------
总群体 详细比较结果
------------------------------------------------------------

1. exponential模型:
   MSE: 0.00116518
   模型类型: 指数变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.263097·exp(-0.065857·t)
     恢复率: μ(t) = 0.052609·exp(-0.021698·t)
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -0.263097·exp(-0.065857·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = 0.263097·exp(-0.065857·t)·I·S - 0.052609·exp(-0.021698·t)·I
     dR/dt = μ(t)·I = 0.052609·exp(-0.021698·t)·I
   平均R₀: 2.578126
   原始参数: [0.2630972681390613, -0.06585700423900495, 0.05260936662259262, -0.021697570549804097]
   R² (S,I,R): (0.9654, 0.7962, 0.9749)
   ★ 最佳模型

2. linear模型:
   MSE: 0.00200356
   模型类型: 线性变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.200042 + -0.005473·t
     恢复率: μ(t) = 0.053702 + -0.000839·t
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.200042 + -0.005473·t)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.200042 + -0.005473·t)·I·S - (0.053702 + -0.000839·t)·I
     dR/dt = μ(t)·I = (0.053702 + -0.000839·t)·I
   平均R₀: 2.438686
   原始参数: [0.2000420117648928, -0.005472614620382159, 0.05370219033142688, -0.0008389965236075786]
   R² (S,I,R): (0.9344, 0.6348, 0.9743)

3. polynomial模型:
   MSE: 0.00250724
   模型类型: 多项式变系数模型
   拟合后的率函数:
     传播率: λ(t) = 0.200992 + -0.005071·t + -0.000002·t²
     恢复率: μ(t) = 0.092598 + -0.005154·t + 0.000103·t²
   拟合后的微分方程:
     dS/dt = -λ(t)·I·S = -(0.200992 + -0.005071·t + -0.000002·t²)·I·S
     dI/dt = λ(t)·I·S - μ(t)·I = (0.200992 + -0.005071·t + -0.000002·t²)·I·S - (0.092598 + -0.005154·t + 0.000103·t²)·I
     dR/dt = μ(t)·I = (0.092598 + -0.005154·t + 0.000103·t²)·I
   平均R₀: 2.184597
   原始参数: [0.20099248016649165, -0.005071351316011243, -1.7421511997294596e-06, 0.09259762330389712, -0.005154190855844892, 0.00010317431603313628]
   R² (S,I,R): (0.9287, 0.4105, 0.9870)

4. constant模型:
   MSE: 0.00744708
   模型类型: 常系数模型
   拟合后的率函数:
     传播率: β = 0.124443
     恢复率: γ = 0.049845
   拟合后的微分方程:
     dS/dt = -0.124443·S·I/N
     dI/dt = 0.124443·S·I/N - 0.049845·I
     dR/dt = 0.049845·I
   R₀: 2.496615
   R² (S,I,R): (0.7206, -0.2090, 0.9318)

============================================================
模型选择建议
============================================================
最佳模型: exponential模型

建议:
1. exponential变系数模型表现最佳，说明传播动力学存在时变特征
2. 变系数模型能更好地捕捉传播过程中的动态变化
3. 建议在实际应用中使用变系数模型进行预测和分析
