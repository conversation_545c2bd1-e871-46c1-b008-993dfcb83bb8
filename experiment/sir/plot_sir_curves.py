#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SIR曲线绘制程序

配置参数：
- 如果需要指定特定目录，请修改下面的 TARGET_LOG_DIR 变量
- 设置为 None 则自动使用最新的日志目录
"""

# ==================== 配置参数 ====================
TARGET_LOG_DIR = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250818_140336"  # 指定目录路径，例如: "experiment/analysis_results/run_20250823_212449"
# TARGET_LOG_DIR = "experiment/analysis_results/run_20250823_212449"  # 取消注释并修改路径来指定目录
# ==================================================

import os
import sys
import json
import glob
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime
import logging
from matplotlib.backends.backend_pdf import PdfPages

# 设置字体 - 确保使用Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 验证字体设置
import matplotlib.font_manager as fm
available_fonts = [f.name for f in fm.fontManager.ttflist if 'Times New Roman' in f.name]
if available_fonts:
    print(f"✓ Times New Roman字体可用")
else:
    print("⚠ Times New Roman字体不可用，将使用默认serif字体")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_latest_log_directory(logs_base_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/analysis_results/") -> str:
    """
    查找最新的日志目录（必须包含以final开头的文件）

    Args:
        logs_base_dir: 日志基础目录路径

    Returns:
        最新的日志目录路径，如果没有找到则返回None
    """
    if not os.path.exists(logs_base_dir):
        logger.error(f"日志基础目录不存在 - {logs_base_dir}")
        return None

    # 查找所有以run_开头的目录
    pattern = os.path.join(logs_base_dir, "run_*")
    directories = glob.glob(pattern)

    if not directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何run_*目录")
        return None

    # 筛选包含以final开头文件的目录
    valid_directories = []
    for directory in directories:
        final_files = glob.glob(os.path.join(directory, "final*"))
        if final_files:
            valid_directories.append(directory)
            logger.debug(f"目录 {directory} 包含 {len(final_files)} 个final文件")
        else:
            logger.debug(f"目录 {directory} 不包含final文件，跳过")

    if not valid_directories:
        logger.error(f"在 {logs_base_dir} 中未找到任何包含final文件的目录")
        return None

    # 按修改时间排序，获取最新的目录
    valid_directories.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_dir = valid_directories[0]

    logger.info(f"找到最新的包含final文件的日志目录: {latest_dir}")
    return latest_dir

def load_sir_data(log_dir: str):
    """
    从日志目录加载所有SIR数据

    Args:
        log_dir: 日志目录路径

    Returns:
        包含所有SIR数据的字典
    """
    if not os.path.exists(log_dir):
        logger.error(f"指定的日志目录不存在: {log_dir}")
        return {}

    sir_files = glob.glob(os.path.join(log_dir, "*sir_metrics.json"))
    sir_data = {}

    for file_path in sir_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取轮次和阶段信息
            round_num = data.get('round', 0)
            stage = data.get('stage', '')
            key = f"round_{round_num}_{stage}"

            sir_data[key] = data

        except Exception as e:
            logger.warning(f"读取文件 {file_path} 时出错: {e}")
            continue

    logger.info(f"从 {log_dir} 成功加载 {len(sir_data)} 个SIR数据文件")
    return sir_data

def extract_sir_trends(sir_data: dict):
    """
    从SIR数据中提取趋势数据
    
    Args:
        sir_data: SIR数据字典
        
    Returns:
        包含总体和各群体趋势数据的字典
    """
    # 获取所有轮次
    rounds = set()
    for key in sir_data.keys():
        if 'round_' in key:
            round_num = int(key.split('_')[1])
            rounds.add(round_num)
    
    rounds = sorted(list(rounds))
    logger.info(f"发现 {len(rounds)} 个轮次的数据: {rounds}")

    if not rounds:
        logger.error("未找到任何轮次数据")
        return None

    # 初始化趋势数据
    trends = {
        'rounds': rounds,
        'total': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        },
        'intellectual': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        },
        'regular': {
            'susceptible': [],
            'infected': [],
            'recovered': []
        }
    }
    
    # 提取每轮的数据（优先使用after数据，如果没有则使用before数据）
    for round_num in rounds:
        after_key = f"round_{round_num}_after"
        before_key = f"round_{round_num}_before"
        
        # 选择数据源
        if after_key in sir_data:
            data = sir_data[after_key]
        elif before_key in sir_data:
            data = sir_data[before_key]
        else:
            logger.warning(f"轮次 {round_num} 没有找到SIR数据")
            # 填充空数据
            trends['total']['susceptible'].append(0)
            trends['total']['infected'].append(0)
            trends['total']['recovered'].append(0)
            trends['intellectual']['susceptible'].append(0)
            trends['intellectual']['infected'].append(0)
            trends['intellectual']['recovered'].append(0)
            trends['regular']['susceptible'].append(0)
            trends['regular']['infected'].append(0)
            trends['regular']['recovered'].append(0)
            continue
        
        # 提取总体数据
        trends['total']['susceptible'].append(data.get('susceptible', {}).get('ratio', 0))
        trends['total']['infected'].append(data.get('infected', {}).get('ratio', 0))
        trends['total']['recovered'].append(data.get('recovered', {}).get('ratio', 0))
        
        # 提取群体数据
        group_analysis = data.get('group_analysis', {})
        
        # 知识分子群体
        intellectual_data = group_analysis.get('intellectual', {})
        trends['intellectual']['susceptible'].append(intellectual_data.get('susceptible', {}).get('ratio', 0))
        trends['intellectual']['infected'].append(intellectual_data.get('infected', {}).get('ratio', 0))
        trends['intellectual']['recovered'].append(intellectual_data.get('recovered', {}).get('ratio', 0))
        
        # 普通群众群体
        regular_data = group_analysis.get('regular', {})
        trends['regular']['susceptible'].append(regular_data.get('susceptible', {}).get('ratio', 0))
        trends['regular']['infected'].append(regular_data.get('infected', {}).get('ratio', 0))
        trends['regular']['recovered'].append(regular_data.get('recovered', {}).get('ratio', 0))
    
    return trends

def plot_sir_curves(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/sir", log_dir_name: str = ""):
    """
    绘制SIR曲线图

    Args:
        trends: 趋势数据
        output_dir: 输出目录
        log_dir_name: 日志目录名称，用于创建子目录
    """
    # 创建以日志目录名命名的子目录
    if log_dir_name:
        output_subdir = os.path.join(output_dir, log_dir_name)
    else:
        output_subdir = output_dir

    os.makedirs(output_subdir, exist_ok=True)
    
    rounds = trends['rounds']
    
    # 设置图形样式
    plt.style.use('default')
    # 莫兰迪色系：柔和的绿红蓝
    colors = {
        'susceptible': '#8FBC8F',  # 莫兰迪绿色
        'infected': '#CD5C5C',     # 莫兰迪红色
        'recovered': '#6495ED'     # 莫兰迪蓝色
    }
    
    # 1. 绘制总群体SIR曲线
    plt.figure(figsize=(6, 3))  # 调整横纵比到2

    # 创建进度标签（使用百分数格式）
    total_rounds = len(rounds)
    period_labels = [f"{(i+1)/total_rounds*100:.0f}%" for i in range(total_rounds)]

    plt.plot(rounds, trends['total']['susceptible'], 'o-', color=colors['susceptible'],
             linewidth=2, markersize=1.5, label='Susceptible')
    plt.plot(rounds, trends['total']['infected'], 's-', color=colors['infected'],
             linewidth=2, markersize=1.5, label='Infected')
    plt.plot(rounds, trends['total']['recovered'], '^-', color=colors['recovered'],
             linewidth=2, markersize=1.5, label='Recovered')

    plt.xlabel('Period', fontsize=10)
    plt.ylabel('Proportion', fontsize=10)
    # 移除标题

    # 设置x轴刻度和标签 - 保留5-8个刻度
    total_rounds = len(rounds)
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45)
    plt.yticks(fontsize=10)

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    total_output_path_png = os.path.join(output_subdir, 'total_sir_curves.png')
    plt.savefig(total_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    total_output_path_pdf = os.path.join(output_subdir, 'total_sir_curves.pdf')
    plt.savefig(total_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"总群体SIR曲线已保存到: {total_output_path_png}")
    logger.info(f"总群体SIR曲线PDF已保存到: {total_output_path_pdf}")
    
    # 2. 绘制知识分子群体SIR曲线
    plt.figure(figsize=(6, 3))  # 与总体曲线保持一致的横纵比

    plt.plot(rounds, trends['intellectual']['susceptible'], 'o-', color=colors['susceptible'],
             linewidth=2, markersize=1.5, label='Susceptible')
    plt.plot(rounds, trends['intellectual']['infected'], 's-', color=colors['infected'],
             linewidth=2, markersize=1.5, label='Infected')
    plt.plot(rounds, trends['intellectual']['recovered'], '^-', color=colors['recovered'],
             linewidth=2, markersize=1.5, label='Recovered')

    plt.xlabel('Period', fontsize=10)
    plt.ylabel('Proportion', fontsize=10)
    # 移除标题

    # 设置x轴刻度和标签 - 保留5-8个刻度
    total_rounds = len(rounds)
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45)
    plt.yticks(fontsize=10)

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    intellectual_output_path_png = os.path.join(output_subdir, 'intellectual_sir_curves.png')
    plt.savefig(intellectual_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    intellectual_output_path_pdf = os.path.join(output_subdir, 'intellectual_sir_curves.pdf')
    plt.savefig(intellectual_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"知识分子群体SIR曲线已保存到: {intellectual_output_path_png}")
    logger.info(f"知识分子群体SIR曲线PDF已保存到: {intellectual_output_path_pdf}")
    
    # 3. 绘制普通群众群体SIR曲线
    plt.figure(figsize=(6, 3))  # 与总体曲线保持一致的横纵比

    plt.plot(rounds, trends['regular']['susceptible'], 'o-', color=colors['susceptible'],
             linewidth=2, markersize=1.5, label='Susceptible')
    plt.plot(rounds, trends['regular']['infected'], 's-', color=colors['infected'],
             linewidth=2, markersize=1.5, label='Infected')
    plt.plot(rounds, trends['regular']['recovered'], '^-', color=colors['recovered'],
             linewidth=2, markersize=1.5, label='Recovered')

    plt.xlabel('Period', fontsize=10)
    plt.ylabel('Proportion', fontsize=10)
    # 移除标题

    # 设置x轴刻度和标签 - 保留5-8个刻度
    total_rounds = len(rounds)
    if total_rounds <= 8:
        tick_indices = list(range(total_rounds))
    else:
        # 均匀分布选择6-7个刻度
        step = max(1, total_rounds // 6)
        tick_indices = list(range(0, total_rounds, step))
        if tick_indices[-1] != total_rounds - 1:
            tick_indices.append(total_rounds - 1)

    selected_rounds = [rounds[i] for i in tick_indices]
    selected_labels = [period_labels[i] for i in tick_indices]

    plt.xticks(selected_rounds, selected_labels, fontsize=10, rotation=45)
    plt.yticks(fontsize=10)

    plt.grid(True, alpha=0.3)
    plt.ylim(0, 1)

    # 图例 - 放在外面，无框
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left', frameon=False)
    plt.tight_layout()

    # 保存PNG格式
    regular_output_path_png = os.path.join(output_subdir, 'regular_sir_curves.png')
    plt.savefig(regular_output_path_png, dpi=300, bbox_inches='tight')

    # 保存PDF格式
    regular_output_path_pdf = os.path.join(output_subdir, 'regular_sir_curves.pdf')
    plt.savefig(regular_output_path_pdf, bbox_inches='tight', format='pdf')

    plt.close()
    logger.info(f"普通群众群体SIR曲线已保存到: {regular_output_path_png}")
    logger.info(f"普通群众群体SIR曲线PDF已保存到: {regular_output_path_pdf}")

    # 4. 保存JSON数据
    save_sir_data_json(trends, output_subdir)

    # 5. 生成中文分析报告
    generate_chinese_report(trends, output_subdir)

def save_sir_data_json(trends: dict, output_dir: str = "/home/<USER>/workspace/virtualcommunity/experiment/sir"):
    """
    保存SIR趋势数据为JSON文件

    Args:
        trends: 趋势数据
        output_dir: 输出目录
    """
    # 构建输出数据
    output_data = {
        "metadata": {
            "generated_at": datetime.now().isoformat(),
            "total_rounds": len(trends['rounds']),
            "rounds_range": f"{min(trends['rounds'])}-{max(trends['rounds'])}",
            "description": "SIR model evolution data for virtual community experiment"
        },
        "rounds": trends['rounds'],
        "total_population": {
            "susceptible": trends['total']['susceptible'],
            "infected": trends['total']['infected'],
            "recovered": trends['total']['recovered']
        },
        "intellectual_group": {
            "susceptible": trends['intellectual']['susceptible'],
            "infected": trends['intellectual']['infected'],
            "recovered": trends['intellectual']['recovered']
        },
        "regular_group": {
            "susceptible": trends['regular']['susceptible'],
            "infected": trends['regular']['infected'],
            "recovered": trends['regular']['recovered']
        }
    }

    # 保存JSON文件
    json_output_path = os.path.join(output_dir, 'sir_curves_data.json')
    with open(json_output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"SIR数据已保存到: {json_output_path}")

def generate_chinese_report(trends: dict, output_dir: str):
    """
    生成中文分析报告

    Args:
        trends: 趋势数据
        output_dir: 输出目录
    """
    rounds = trends['rounds']
    total_rounds = len(rounds)

    # 计算关键指标
    def calculate_metrics(data):
        """计算各种指标"""
        susceptible = data['susceptible']
        infected = data['infected']
        recovered = data['recovered']

        # 初始和最终状态
        initial_s, initial_i, initial_r = susceptible[0], infected[0], recovered[0]
        final_s, final_i, final_r = susceptible[-1], infected[-1], recovered[-1]

        # 感染峰值
        max_infected = max(infected)
        max_infected_round = infected.index(max_infected) + 1

        # 恢复率变化
        recovery_change = final_r - initial_r

        # 易感人群减少率
        susceptible_reduction = initial_s - final_s

        return {
            'initial': {'s': initial_s, 'i': initial_i, 'r': initial_r},
            'final': {'s': final_s, 'i': final_i, 'r': final_r},
            'max_infected': max_infected,
            'max_infected_round': max_infected_round,
            'recovery_change': recovery_change,
            'susceptible_reduction': susceptible_reduction
        }

    # 计算各群体指标
    total_metrics = calculate_metrics(trends['total'])
    intellectual_metrics = calculate_metrics(trends['intellectual'])
    regular_metrics = calculate_metrics(trends['regular'])

    # 生成报告内容
    report_content = f"""虚拟社区SIR模型传播分析报告

生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
分析轮次：第{rounds[0]}轮至第{rounds[-1]}轮，共{total_rounds}轮

==================================================
一、总体传播情况分析
==================================================

1. 初始状态分布：
   - 易感人群比例：{total_metrics['initial']['s']:.1%}
   - 感染人群比例：{total_metrics['initial']['i']:.1%}
   - 恢复人群比例：{total_metrics['initial']['r']:.1%}

2. 最终状态分布：
   - 易感人群比例：{total_metrics['final']['s']:.1%}
   - 感染人群比例：{total_metrics['final']['i']:.1%}
   - 恢复人群比例：{total_metrics['final']['r']:.1%}

3. 传播特征：
   - 感染峰值：{total_metrics['max_infected']:.1%}（第{total_metrics['max_infected_round']}轮达到）
   - 易感人群减少：{total_metrics['susceptible_reduction']:.1%}
   - 恢复人群增长：{total_metrics['recovery_change']:.1%}

==================================================
二、知识分子群体传播分析
==================================================

1. 初始状态分布：
   - 易感人群比例：{intellectual_metrics['initial']['s']:.1%}
   - 感染人群比例：{intellectual_metrics['initial']['i']:.1%}
   - 恢复人群比例：{intellectual_metrics['initial']['r']:.1%}

2. 最终状态分布：
   - 易感人群比例：{intellectual_metrics['final']['s']:.1%}
   - 感染人群比例：{intellectual_metrics['final']['i']:.1%}
   - 恢复人群比例：{intellectual_metrics['final']['r']:.1%}

3. 传播特征：
   - 感染峰值：{intellectual_metrics['max_infected']:.1%}（第{intellectual_metrics['max_infected_round']}轮达到）
   - 易感人群减少：{intellectual_metrics['susceptible_reduction']:.1%}
   - 恢复人群增长：{intellectual_metrics['recovery_change']:.1%}

==================================================
三、普通群众群体传播分析
==================================================

1. 初始状态分布：
   - 易感人群比例：{regular_metrics['initial']['s']:.1%}
   - 感染人群比例：{regular_metrics['initial']['i']:.1%}
   - 恢复人群比例：{regular_metrics['initial']['r']:.1%}

2. 最终状态分布：
   - 易感人群比例：{regular_metrics['final']['s']:.1%}
   - 感染人群比例：{regular_metrics['final']['i']:.1%}
   - 恢复人群比例：{regular_metrics['final']['r']:.1%}

3. 传播特征：
   - 感染峰值：{regular_metrics['max_infected']:.1%}（第{regular_metrics['max_infected_round']}轮达到）
   - 易感人群减少：{regular_metrics['susceptible_reduction']:.1%}
   - 恢复人群增长：{regular_metrics['recovery_change']:.1%}

==================================================
四、群体间差异对比分析
==================================================

1. 感染峰值对比：
   - 知识分子群体：{intellectual_metrics['max_infected']:.1%}
   - 普通群众群体：{regular_metrics['max_infected']:.1%}
   - 总体水平：{total_metrics['max_infected']:.1%}
   - 差异分析：{'知识分子群体感染峰值更高' if intellectual_metrics['max_infected'] > regular_metrics['max_infected'] else '普通群众群体感染峰值更高' if regular_metrics['max_infected'] > intellectual_metrics['max_infected'] else '两群体感染峰值相近'}

2. 恢复能力对比：
   - 知识分子群体恢复率增长：{intellectual_metrics['recovery_change']:.1%}
   - 普通群众群体恢复率增长：{regular_metrics['recovery_change']:.1%}
   - 差异分析：{'知识分子群体恢复能力更强' if intellectual_metrics['recovery_change'] > regular_metrics['recovery_change'] else '普通群众群体恢复能力更强' if regular_metrics['recovery_change'] > intellectual_metrics['recovery_change'] else '两群体恢复能力相近'}

3. 易感性变化对比：
   - 知识分子群体易感人群减少：{intellectual_metrics['susceptible_reduction']:.1%}
   - 普通群众群体易感人群减少：{regular_metrics['susceptible_reduction']:.1%}
   - 差异分析：{'知识分子群体更容易被感染' if intellectual_metrics['susceptible_reduction'] > regular_metrics['susceptible_reduction'] else '普通群众群体更容易被感染' if regular_metrics['susceptible_reduction'] > intellectual_metrics['susceptible_reduction'] else '两群体易感性相近'}

4. 感染峰值时间对比：
   - 知识分子群体：第{intellectual_metrics['max_infected_round']}轮
   - 普通群众群体：第{regular_metrics['max_infected_round']}轮
   - 总体：第{total_metrics['max_infected_round']}轮
   - 时间差异：{abs(intellectual_metrics['max_infected_round'] - regular_metrics['max_infected_round'])}轮

==================================================
五、传播模式总结
==================================================

基于{total_rounds}轮的观察数据，本次虚拟社区信息传播呈现以下特点：

1. 传播规模：总体感染峰值达到{total_metrics['max_infected']:.1%}，表明信息在社区中{'广泛传播' if total_metrics['max_infected'] > 0.5 else '有限传播'}。

2. 群体差异：{'知识分子群体和普通群众群体在传播模式上存在显著差异' if abs(intellectual_metrics['max_infected'] - regular_metrics['max_infected']) > 0.1 else '两个群体的传播模式较为相似'}。

3. 恢复特征：最终恢复率达到{total_metrics['final']['r']:.1%}，显示社区具有{'良好' if total_metrics['final']['r'] > 0.7 else '一般' if total_metrics['final']['r'] > 0.4 else '较弱'}的信息消化能力。

4. 传播时效：感染峰值出现在第{total_metrics['max_infected_round']}轮，占总轮次的{total_metrics['max_infected_round']/total_rounds:.1%}，表明传播{'快速达到高峰' if total_metrics['max_infected_round']/total_rounds < 0.3 else '逐步达到高峰' if total_metrics['max_infected_round']/total_rounds < 0.7 else '后期才达到高峰'}。

==================================================
报告结束
==================================================
"""

    # 保存报告
    report_path = os.path.join(output_dir, 'SIR传播分析报告.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    logger.info(f"中文分析报告已保存到: {report_path}")

def main():
    """
    主函数
    """
    logger.info("开始绘制SIR曲线...")

    # 1. 确定日志目录 - 优先使用配置参数
    if TARGET_LOG_DIR:
        log_dir = TARGET_LOG_DIR
        logger.info(f"使用配置指定的日志目录: {log_dir}")
    else:
        log_dir = find_latest_log_directory()
        if not log_dir:
            logger.error("未找到日志目录，程序退出")
            return 1

    # 提取日志目录名称
    log_dir_name = os.path.basename(log_dir)

    # 2. 加载SIR数据
    sir_data = load_sir_data(log_dir)
    if not sir_data:
        logger.error("未找到SIR数据，程序退出")
        return 1

    # 3. 提取趋势数据
    trends = extract_sir_trends(sir_data)
    if not trends:
        logger.error("提取趋势数据失败，程序退出")
        return 1

    # 4. 绘制曲线图
    plot_sir_curves(trends, "/home/<USER>/workspace/virtualcommunity/experiment/sir", log_dir_name)

    logger.info("SIR曲线绘制完成！")
    return 0

if __name__ == "__main__":
    sys.exit(main())
